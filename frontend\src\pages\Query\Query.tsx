import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  TextField,
  Button,
  Paper,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Divider,
  IconButton,
} from '@mui/material';
import {
  Send as SendIcon,
  Psychology as AIIcon,
  History as HistoryIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface QueryResult {
  id: string;
  question: string;
  answer: string;
  timestamp: string;
  confidence: number;
  sources: string[];
}

const Query: React.FC = () => {
  const [query, setQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [expandedResults, setExpandedResults] = useState<Set<string>>(new Set());
  const [results, setResults] = useState<QueryResult[]>([
    {
      id: '1',
      question: '<PERSON><PERSON><PERSON><PERSON> kiện thành lập doanh nghiệ<PERSON> tư nhân là gì?',
      answer: '<PERSON>hi<PERSON> 2020, đi<PERSON><PERSON> kiện thành lập do<PERSON>h nghiệp tư nhân bao gồm: 1) Chủ doanh nghiệp tư nhân phải là cá nhân có đủ năng lực hành vi dân sự; 2) Không thuộc đối tượng bị cấm thành lập doanh nghiệp; 3) Có địa chỉ trụ sở chính tại Việt Nam. Ngoài ra, chủ doanh nghiệp tư nhân cần có vốn pháp định tối thiểu theo quy định của pháp luật chuyên ngành (nếu có) và phải đăng ký kinh doanh theo đúng thủ tục.',
      timestamp: '2024-01-15 10:30',
      confidence: 0.95,
      sources: ['Luật Doanh nghiệp 2020', 'Nghị định 01/2021/NĐ-CP'],
    },
    {
      id: '2',
      question: 'Thủ tục ly hôn đơn phương như thế nào?',
      answer: 'Ly hôn đơn phương là trường hợp chỉ có một bên vợ hoặc chồng yêu cầu ly hôn. Theo Bộ luật Dân sự 2015, thủ tục ly hôn đơn phương bao gồm: 1) Nộp đơn khởi kiện lên Tòa án có thẩm quyền; 2) Tòa án tiến hành hòa giải; 3) Nếu hòa giải không thành, Tòa án sẽ xét xử và ra quyết định ly hôn.',
      timestamp: '2024-01-14 15:20',
      confidence: 0.88,
      sources: ['Bộ luật Dân sự 2015', 'Luật Hôn nhân và Gia đình 2014'],
    },
  ]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const newResult: QueryResult = {
        id: Date.now().toString(),
        question: query,
        answer: 'Đây là câu trả lời mẫu từ AI. Trong thực tế, đây sẽ là phản hồi từ hệ thống AI phân tích tài liệu pháp luật.',
        timestamp: new Date().toLocaleString('vi-VN'),
        confidence: 0.88,
        sources: ['Tài liệu liên quan 1', 'Tài liệu liên quan 2'],
      };
      
      setResults([newResult, ...results]);
      setQuery('');
      setLoading(false);
    }, 2000);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'success';
    if (confidence >= 0.7) return 'warning';
    return 'error';
  };

  const toggleExpanded = (resultId: string) => {
    const newExpanded = new Set(expandedResults);
    if (newExpanded.has(resultId)) {
      newExpanded.delete(resultId);
    } else {
      newExpanded.add(resultId);
    }
    setExpandedResults(newExpanded);
  };

  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" gutterBottom>
          Truy vấn AI
        </Typography>
        <Paper sx={{ p: 3, mb: 3, backgroundColor: 'info.light', color: 'info.contrastText' }}>
          <Typography variant="h6" gutterBottom>
            🤖 LegalMind Pro hoạt động như thế nào?
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            LegalMind Pro là trợ lý AI pháp luật thông minh có khả năng:
          </Typography>
          <Box sx={{ ml: 2, mb: 2 }}>
            <Typography variant="body2" component="div">
              • <strong>Phân tích tài liệu đã tải lên:</strong> Trả lời câu hỏi dựa trên các văn bản pháp luật, hợp đồng, quyết định mà bạn đã upload
            </Typography>
            <Typography variant="body2" component="div">
              • <strong>Tư vấn pháp luật tổng quát:</strong> Cung cấp thông tin về pháp luật Việt Nam từ cơ sở dữ liệu được cập nhật
            </Typography>
            <Typography variant="body2" component="div">
              • <strong>Phân tích và so sánh:</strong> Đối chiếu các quy định pháp luật với tình huống cụ thể
            </Typography>
          </Box>
          <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
            💡 Để có câu trả lời chính xác nhất, hãy tải lên các tài liệu liên quan trong mục "Tài liệu"
          </Typography>
        </Paper>

        {/* Query Input */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Đặt câu hỏi pháp luật
          </Typography>
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              multiline
              rows={4}
              variant="outlined"
              placeholder="Nhập câu hỏi của bạn về pháp luật..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              disabled={loading}
              sx={{ mb: 2 }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                type="submit"
                variant="contained"
                startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                disabled={loading || !query.trim()}
                size="large"
              >
                {loading ? 'Đang xử lý...' : 'Gửi câu hỏi'}
              </Button>
            </Box>
          </form>
        </Paper>

        {/* Results */}
        <Box>
          <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <HistoryIcon sx={{ mr: 1 }} />
            Lịch sử truy vấn
          </Typography>
          
          {results.map((result, index) => (
            <motion.div
              key={result.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  {/* Question */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Typography variant="h6" gutterBottom sx={{ flex: 1 }}>
                      {result.question}
                    </Typography>
                    <IconButton
                      onClick={() => toggleExpanded(result.id)}
                      size="small"
                    >
                      {expandedResults.has(result.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  {/* Answer */}
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                    <AIIcon color="primary" sx={{ mr: 1, mt: 0.5 }} />
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                        {expandedResults.has(result.id)
                          ? result.answer
                          : truncateText(result.answer)
                        }
                      </Typography>
                      {result.answer.length > 200 && !expandedResults.has(result.id) && (
                        <Button
                          size="small"
                          onClick={() => toggleExpanded(result.id)}
                          sx={{ mt: 1, p: 0, minWidth: 'auto' }}
                        >
                          Xem thêm
                        </Button>
                      )}
                    </Box>
                  </Box>
                  
                  {/* Metadata */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mt: 2 }}>
                    <Chip
                      label={`Độ tin cậy: ${(result.confidence * 100).toFixed(0)}%`}
                      color={getConfidenceColor(result.confidence)}
                      size="small"
                    />
                    <Typography variant="caption" color="textSecondary">
                      {result.timestamp}
                    </Typography>
                  </Box>
                  
                  {/* Sources */}
                  {result.sources.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Nguồn tham khảo:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {result.sources.map((source, idx) => (
                          <Chip
                            key={idx}
                            label={source}
                            variant="outlined"
                            size="small"
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
          
          {results.length === 0 && (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body1" color="textSecondary">
                Chưa có truy vấn nào. Hãy bắt đầu bằng cách đặt câu hỏi đầu tiên.
              </Typography>
            </Paper>
          )}
        </Box>
      </motion.div>
    </Box>
  );
};

export default Query;
