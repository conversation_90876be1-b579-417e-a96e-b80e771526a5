{"ast": null, "code": "var _jsxFileName = \"E:\\\\Project\\\\AI\\\\RAG\\\\Law\\\\LegalMind Pro - AI Assistant Ph\\xE1p Lu\\u1EADt Th\\xF4ng Minh\\\\frontend\\\\src\\\\pages\\\\Analytics\\\\Analytics.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CircularProgress, Alert, Avatar, IconButton, Tooltip } from '@mui/material';\nimport { Description, Search, Star, Storage, Refresh } from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Analytics = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchAnalytics();\n  }, []);\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Mock data for now - replace with actual API call\n      const mockData = {\n        overview: {\n          totalDocuments: 24,\n          totalQueries: 156,\n          totalStorageUsed: 245 * 1024 * 1024,\n          // 245 MB\n          averageRating: 4.2\n        },\n        trends: {\n          documents: [5, 8, 12, 15, 18, 22, 24],\n          queries: [20, 35, 45, 78, 95, 125, 156],\n          ratings: [3.8, 3.9, 4.0, 4.1, 4.1, 4.2, 4.2]\n        },\n        categories: {\n          documents: {\n            'Dân sự': 8,\n            'Hình sự': 3,\n            'Hành chính': 5,\n            'Thương mại': 6,\n            'Lao động': 2\n          },\n          queries: {\n            'Tư vấn pháp lý': 89,\n            'Tìm kiếm tài liệu': 34,\n            'Phân tích vụ việc': 21,\n            'Tra cứu quy định': 12\n          }\n        },\n        performance: {\n          averageProcessingTime: 2.3,\n          successRate: 0.96,\n          userSatisfaction: 4.2\n        }\n      };\n      setTimeout(() => {\n        setData(mockData);\n        setLoading(false);\n      }, 1000);\n    } catch (err) {\n      setError('Không thể tải dữ liệu thống kê');\n      setLoading(false);\n    }\n  };\n  const formatBytes = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true\n      }\n    }\n  };\n  const lineChartData = {\n    labels: ['Tuần 1', 'Tuần 2', 'Tuần 3', 'Tuần 4', 'Tuần 5', 'Tuần 6', 'Tuần 7'],\n    datasets: [{\n      label: 'Tài liệu',\n      data: (data === null || data === void 0 ? void 0 : data.trends.documents) || [],\n      borderColor: 'rgb(75, 192, 192)',\n      backgroundColor: 'rgba(75, 192, 192, 0.2)'\n    }, {\n      label: 'Truy vấn',\n      data: (data === null || data === void 0 ? void 0 : data.trends.queries) || [],\n      borderColor: 'rgb(255, 99, 132)',\n      backgroundColor: 'rgba(255, 99, 132, 0.2)'\n    }]\n  };\n  const doughnutData = {\n    labels: Object.keys((data === null || data === void 0 ? void 0 : data.categories.documents) || {}),\n    datasets: [{\n      data: Object.values((data === null || data === void 0 ? void 0 : data.categories.documents) || {}),\n      backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']\n    }]\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '60vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        action: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: fetchAnalytics,\n          children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 4,\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            gutterBottom: true,\n            children: \"Th\\u1ED1ng k\\xEA & Ph\\xE2n t\\xEDch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"textSecondary\",\n            children: \"T\\u1ED5ng quan v\\u1EC1 ho\\u1EA1t \\u0111\\u1ED9ng v\\xE0 hi\\u1EC7u su\\u1EA5t c\\u1EE7a h\\u1EC7 th\\u1ED1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"L\\xE0m m\\u1EDBi d\\u1EEF li\\u1EC7u\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: fetchAnalytics,\n            children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: 'primary.main',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Description, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"T\\xE0i li\\u1EC7u\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"div\",\n                  children: data === null || data === void 0 ? void 0 : data.overview.totalDocuments\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"T\\u1ED5ng s\\u1ED1 t\\xE0i li\\u1EC7u \\u0111\\xE3 t\\u1EA3i l\\xEAn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: 'secondary.main',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"Truy v\\u1EA5n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"div\",\n                  children: data === null || data === void 0 ? void 0 : data.overview.totalQueries\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"T\\u1ED5ng s\\u1ED1 c\\xE2u h\\u1ECFi \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c x\\u1EED l\\xFD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: 'success.main',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Storage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"Dung l\\u01B0\\u1EE3ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"div\",\n                  children: formatBytes((data === null || data === void 0 ? void 0 : data.overview.totalStorageUsed) || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Dung l\\u01B0\\u1EE3ng \\u0111\\xE3 s\\u1EED d\\u1EE5ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.4\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: 'warning.main',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"\\u0110\\xE1nh gi\\xE1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"div\",\n                  children: [data === null || data === void 0 ? void 0 : data.overview.averageRating, \"/5\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"\\u0110\\xE1nh gi\\xE1 trung b\\xECnh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Xu h\\u01B0\\u1EDBng theo th\\u1EDDi gian\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                data: lineChartData,\n                options: chartOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Ph\\xE2n lo\\u1EA1i t\\xE0i li\\u1EC7u\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Doughnut, {\n                data: doughnutData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"RiL7vLwmC7ZWXKL/bXt2EIBjBYk=\");\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Avatar", "IconButton", "<PERSON><PERSON><PERSON>", "Description", "Search", "Star", "Storage", "Refresh", "motion", "jsxDEV", "_jsxDEV", "Analytics", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "fetchAnalytics", "mockData", "overview", "totalDocuments", "totalQueries", "totalStorageUsed", "averageRating", "trends", "documents", "queries", "ratings", "categories", "performance", "averageProcessingTime", "successRate", "userSatisfaction", "setTimeout", "err", "formatBytes", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "chartOptions", "responsive", "plugins", "legend", "position", "scales", "y", "beginAtZero", "lineChartData", "labels", "datasets", "label", "borderColor", "backgroundColor", "doughnutData", "Object", "keys", "values", "sx", "display", "justifyContent", "alignItems", "height", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "severity", "action", "onClick", "div", "initial", "opacity", "animate", "transition", "duration", "mb", "variant", "gutterBottom", "color", "title", "container", "spacing", "item", "xs", "sm", "md", "delay", "bgcolor", "mr", "component", "Line", "options", "Doughnut", "_c", "$RefreshReg$"], "sources": ["E:/Project/AI/RAG/Law/LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>/frontend/src/pages/Analytics/Analytics.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n  Chip,\n  Avatar,\n  LinearProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Analytics as AnalyticsIcon,\n  TrendingUp,\n  Description,\n  Search,\n  Star,\n  Storage,\n  Refresh,\n  Schedule,\n  CheckCircle,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\n\ninterface AnalyticsData {\n  overview: {\n    totalDocuments: number;\n    totalQueries: number;\n    totalStorageUsed: number;\n    averageRating: number;\n  };\n  trends: {\n    documents: number[];\n    queries: number[];\n    ratings: number[];\n  };\n  categories: {\n    documents: Record<string, number>;\n    queries: Record<string, number>;\n  };\n  performance: {\n    averageProcessingTime: number;\n    successRate: number;\n    userSatisfaction: number;\n  };\n}\n\nconst Analytics: React.FC = () => {\n  const [data, setData] = useState<AnalyticsData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchAnalytics();\n  }, []);\n\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // Mock data for now - replace with actual API call\n      const mockData: AnalyticsData = {\n        overview: {\n          totalDocuments: 24,\n          totalQueries: 156,\n          totalStorageUsed: 245 * 1024 * 1024, // 245 MB\n          averageRating: 4.2,\n        },\n        trends: {\n          documents: [5, 8, 12, 15, 18, 22, 24],\n          queries: [20, 35, 45, 78, 95, 125, 156],\n          ratings: [3.8, 3.9, 4.0, 4.1, 4.1, 4.2, 4.2],\n        },\n        categories: {\n          documents: {\n            'Dân sự': 8,\n            'Hình sự': 3,\n            'Hành chính': 5,\n            'Thương mại': 6,\n            'Lao động': 2,\n          },\n          queries: {\n            'Tư vấn pháp lý': 89,\n            'Tìm kiếm tài liệu': 34,\n            'Phân tích vụ việc': 21,\n            'Tra cứu quy định': 12,\n          },\n        },\n        performance: {\n          averageProcessingTime: 2.3,\n          successRate: 0.96,\n          userSatisfaction: 4.2,\n        },\n      };\n\n      setTimeout(() => {\n        setData(mockData);\n        setLoading(false);\n      }, 1000);\n    } catch (err) {\n      setError('Không thể tải dữ liệu thống kê');\n      setLoading(false);\n    }\n  };\n\n  const formatBytes = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n      },\n    },\n  };\n\n  const lineChartData = {\n    labels: ['Tuần 1', 'Tuần 2', 'Tuần 3', 'Tuần 4', 'Tuần 5', 'Tuần 6', 'Tuần 7'],\n    datasets: [\n      {\n        label: 'Tài liệu',\n        data: data?.trends.documents || [],\n        borderColor: 'rgb(75, 192, 192)',\n        backgroundColor: 'rgba(75, 192, 192, 0.2)',\n      },\n      {\n        label: 'Truy vấn',\n        data: data?.trends.queries || [],\n        borderColor: 'rgb(255, 99, 132)',\n        backgroundColor: 'rgba(255, 99, 132, 0.2)',\n      },\n    ],\n  };\n\n  const doughnutData = {\n    labels: Object.keys(data?.categories.documents || {}),\n    datasets: [\n      {\n        data: Object.values(data?.categories.documents || {}),\n        backgroundColor: [\n          '#FF6384',\n          '#36A2EB',\n          '#FFCE56',\n          '#4BC0C0',\n          '#9966FF',\n        ],\n      },\n    ],\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"error\" action={\n          <IconButton onClick={fetchAnalytics}>\n            <Refresh />\n          </IconButton>\n        }>\n          {error}\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        {/* Header */}\n        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Box>\n            <Typography variant=\"h4\" gutterBottom>\n              Thống kê & Phân tích\n            </Typography>\n            <Typography variant=\"body1\" color=\"textSecondary\">\n              Tổng quan về hoạt động và hiệu suất của hệ thống\n            </Typography>\n          </Box>\n          <Tooltip title=\"Làm mới dữ liệu\">\n            <IconButton onClick={fetchAnalytics}>\n              <Refresh />\n            </IconButton>\n          </Tooltip>\n        </Box>\n\n        {/* Overview Cards */}\n        <Grid container spacing={3} sx={{ mb: 4 }}>\n          <Grid item xs={12} sm={6} md={3}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>\n                      <Description />\n                    </Avatar>\n                    <Typography variant=\"h6\">Tài liệu</Typography>\n                  </Box>\n                  <Typography variant=\"h4\" component=\"div\">\n                    {data?.overview.totalDocuments}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Tổng số tài liệu đã tải lên\n                  </Typography>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>\n                      <Search />\n                    </Avatar>\n                    <Typography variant=\"h6\">Truy vấn</Typography>\n                  </Box>\n                  <Typography variant=\"h4\" component=\"div\">\n                    {data?.overview.totalQueries}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Tổng số câu hỏi đã được xử lý\n                  </Typography>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n            >\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>\n                      <Storage />\n                    </Avatar>\n                    <Typography variant=\"h6\">Dung lượng</Typography>\n                  </Box>\n                  <Typography variant=\"h4\" component=\"div\">\n                    {formatBytes(data?.overview.totalStorageUsed || 0)}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Dung lượng đã sử dụng\n                  </Typography>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n            >\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>\n                      <Star />\n                    </Avatar>\n                    <Typography variant=\"h6\">Đánh giá</Typography>\n                  </Box>\n                  <Typography variant=\"h4\" component=\"div\">\n                    {data?.overview.averageRating}/5\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Đánh giá trung bình\n                  </Typography>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n        </Grid>\n\n        {/* Charts */}\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={8}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Xu hướng theo thời gian\n                </Typography>\n                <Line data={lineChartData} options={chartOptions} />\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} md={4}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Phân loại tài liệu\n                </Typography>\n                <Doughnut data={doughnutData} />\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </motion.div>\n    </Box>\n  );\n};\n\nexport default Analytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,gBAAgB,EAChBC,KAAK,EAELC,MAAM,EAGNC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SAGEC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,OAAO,QAGF,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAyBvC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAuB,IAAI,CAAC;EAC5D,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd2B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAME,QAAuB,GAAG;QAC9BC,QAAQ,EAAE;UACRC,cAAc,EAAE,EAAE;UAClBC,YAAY,EAAE,GAAG;UACjBC,gBAAgB,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;UAAE;UACrCC,aAAa,EAAE;QACjB,CAAC;QACDC,MAAM,EAAE;UACNC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UACrCC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UACvCC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QAC7C,CAAC;QACDC,UAAU,EAAE;UACVH,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,CAAC;YACZ,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YACf,UAAU,EAAE;UACd,CAAC;UACDC,OAAO,EAAE;YACP,gBAAgB,EAAE,EAAE;YACpB,mBAAmB,EAAE,EAAE;YACvB,mBAAmB,EAAE,EAAE;YACvB,kBAAkB,EAAE;UACtB;QACF,CAAC;QACDG,WAAW,EAAE;UACXC,qBAAqB,EAAE,GAAG;UAC1BC,WAAW,EAAE,IAAI;UACjBC,gBAAgB,EAAE;QACpB;MACF,CAAC;MAEDC,UAAU,CAAC,MAAM;QACfrB,OAAO,CAACM,QAAQ,CAAC;QACjBJ,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZlB,QAAQ,CAAC,gCAAgC,CAAC;MAC1CF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,WAAW,GAAIC,KAAa,IAAK;IACrC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE;MACf;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAC9EC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,UAAU;MACjB9C,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,MAAM,CAACC,SAAS,KAAI,EAAE;MAClCiC,WAAW,EAAE,mBAAmB;MAChCC,eAAe,EAAE;IACnB,CAAC,EACD;MACEF,KAAK,EAAE,UAAU;MACjB9C,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,MAAM,CAACE,OAAO,KAAI,EAAE;MAChCgC,WAAW,EAAE,mBAAmB;MAChCC,eAAe,EAAE;IACnB,CAAC;EAEL,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBL,MAAM,EAAEM,MAAM,CAACC,IAAI,CAAC,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,UAAU,CAACH,SAAS,KAAI,CAAC,CAAC,CAAC;IACrD+B,QAAQ,EAAE,CACR;MACE7C,IAAI,EAAEkD,MAAM,CAACE,MAAM,CAAC,CAAApD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,UAAU,CAACH,SAAS,KAAI,CAAC,CAAC,CAAC;MACrDkC,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS;IAEb,CAAC;EAEL,CAAC;EAED,IAAI9C,OAAO,EAAE;IACX,oBACEL,OAAA,CAACjB,GAAG;MAACyE,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAC,QAAA,eAC3F7D,OAAA,CAACZ,gBAAgB;QAAC0E,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAI3D,KAAK,EAAE;IACT,oBACEP,OAAA,CAACjB,GAAG;MAACyE,EAAE,EAAE;QAAEW,CAAC,EAAE;MAAE,CAAE;MAAAN,QAAA,eAChB7D,OAAA,CAACX,KAAK;QAAC+E,QAAQ,EAAC,OAAO;QAACC,MAAM,eAC5BrE,OAAA,CAACT,UAAU;UAAC+E,OAAO,EAAE7D,cAAe;UAAAoD,QAAA,eAClC7D,OAAA,CAACH,OAAO;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACb;QAAAL,QAAA,EACEtD;MAAK;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACElE,OAAA,CAACjB,GAAG;IAACyE,EAAE,EAAE;MAAEW,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,eAChB7D,OAAA,CAACF,MAAM,CAACyE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAE7B,CAAC,EAAE;MAAG,CAAE;MAC/B8B,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAE7B,CAAC,EAAE;MAAE,CAAE;MAC9B+B,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAf,QAAA,gBAG9B7D,OAAA,CAACjB,GAAG;QAACyE,EAAE,EAAE;UAAEqB,EAAE,EAAE,CAAC;UAAEpB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACzF7D,OAAA,CAACjB,GAAG;UAAA8E,QAAA,gBACF7D,OAAA,CAAChB,UAAU;YAAC8F,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAlB,QAAA,EAAC;UAEtC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblE,OAAA,CAAChB,UAAU;YAAC8F,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,eAAe;YAAAnB,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNlE,OAAA,CAACR,OAAO;UAACyF,KAAK,EAAC,mCAAiB;UAAApB,QAAA,eAC9B7D,OAAA,CAACT,UAAU;YAAC+E,OAAO,EAAE7D,cAAe;YAAAoD,QAAA,eAClC7D,OAAA,CAACH,OAAO;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGNlE,OAAA,CAACf,IAAI;QAACiG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC3B,EAAE,EAAE;UAAEqB,EAAE,EAAE;QAAE,CAAE;QAAAhB,QAAA,gBACxC7D,OAAA,CAACf,IAAI;UAACmG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA1B,QAAA,eAC9B7D,OAAA,CAACF,MAAM,CAACyE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE7B,CAAC,EAAE;YAAG,CAAE;YAC/B8B,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE7B,CAAC,EAAE;YAAE,CAAE;YAC9B+B,UAAU,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAA3B,QAAA,eAE3B7D,OAAA,CAACd,IAAI;cAAA2E,QAAA,eACH7D,OAAA,CAACb,WAAW;gBAAA0E,QAAA,gBACV7D,OAAA,CAACjB,GAAG;kBAACyE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEkB,EAAE,EAAE;kBAAE,CAAE;kBAAAhB,QAAA,gBACxD7D,OAAA,CAACV,MAAM;oBAACkE,EAAE,EAAE;sBAAEiC,OAAO,EAAE,cAAc;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAA7B,QAAA,eAC7C7D,OAAA,CAACP,WAAW;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACTlE,OAAA,CAAChB,UAAU;oBAAC8F,OAAO,EAAC,IAAI;oBAAAjB,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNlE,OAAA,CAAChB,UAAU;kBAAC8F,OAAO,EAAC,IAAI;kBAACa,SAAS,EAAC,KAAK;kBAAA9B,QAAA,EACrC1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,CAACC;gBAAc;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACblE,OAAA,CAAChB,UAAU;kBAAC8F,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAnB,QAAA,EAAC;gBAElD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEPlE,OAAA,CAACf,IAAI;UAACmG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA1B,QAAA,eAC9B7D,OAAA,CAACF,MAAM,CAACyE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE7B,CAAC,EAAE;YAAG,CAAE;YAC/B8B,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE7B,CAAC,EAAE;YAAE,CAAE;YAC9B+B,UAAU,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAA3B,QAAA,eAE3B7D,OAAA,CAACd,IAAI;cAAA2E,QAAA,eACH7D,OAAA,CAACb,WAAW;gBAAA0E,QAAA,gBACV7D,OAAA,CAACjB,GAAG;kBAACyE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEkB,EAAE,EAAE;kBAAE,CAAE;kBAAAhB,QAAA,gBACxD7D,OAAA,CAACV,MAAM;oBAACkE,EAAE,EAAE;sBAAEiC,OAAO,EAAE,gBAAgB;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAA7B,QAAA,eAC/C7D,OAAA,CAACN,MAAM;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACTlE,OAAA,CAAChB,UAAU;oBAAC8F,OAAO,EAAC,IAAI;oBAAAjB,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNlE,OAAA,CAAChB,UAAU;kBAAC8F,OAAO,EAAC,IAAI;kBAACa,SAAS,EAAC,KAAK;kBAAA9B,QAAA,EACrC1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,CAACE;gBAAY;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACblE,OAAA,CAAChB,UAAU;kBAAC8F,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAnB,QAAA,EAAC;gBAElD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEPlE,OAAA,CAACf,IAAI;UAACmG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA1B,QAAA,eAC9B7D,OAAA,CAACF,MAAM,CAACyE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE7B,CAAC,EAAE;YAAG,CAAE;YAC/B8B,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE7B,CAAC,EAAE;YAAE,CAAE;YAC9B+B,UAAU,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAA3B,QAAA,eAE3B7D,OAAA,CAACd,IAAI;cAAA2E,QAAA,eACH7D,OAAA,CAACb,WAAW;gBAAA0E,QAAA,gBACV7D,OAAA,CAACjB,GAAG;kBAACyE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEkB,EAAE,EAAE;kBAAE,CAAE;kBAAAhB,QAAA,gBACxD7D,OAAA,CAACV,MAAM;oBAACkE,EAAE,EAAE;sBAAEiC,OAAO,EAAE,cAAc;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAA7B,QAAA,eAC7C7D,OAAA,CAACJ,OAAO;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACTlE,OAAA,CAAChB,UAAU;oBAAC8F,OAAO,EAAC,IAAI;oBAAAjB,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNlE,OAAA,CAAChB,UAAU;kBAAC8F,OAAO,EAAC,IAAI;kBAACa,SAAS,EAAC,KAAK;kBAAA9B,QAAA,EACrClC,WAAW,CAAC,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,CAACG,gBAAgB,KAAI,CAAC;gBAAC;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACblE,OAAA,CAAChB,UAAU;kBAAC8F,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAnB,QAAA,EAAC;gBAElD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEPlE,OAAA,CAACf,IAAI;UAACmG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA1B,QAAA,eAC9B7D,OAAA,CAACF,MAAM,CAACyE,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE7B,CAAC,EAAE;YAAG,CAAE;YAC/B8B,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE7B,CAAC,EAAE;YAAE,CAAE;YAC9B+B,UAAU,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAA3B,QAAA,eAE3B7D,OAAA,CAACd,IAAI;cAAA2E,QAAA,eACH7D,OAAA,CAACb,WAAW;gBAAA0E,QAAA,gBACV7D,OAAA,CAACjB,GAAG;kBAACyE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEkB,EAAE,EAAE;kBAAE,CAAE;kBAAAhB,QAAA,gBACxD7D,OAAA,CAACV,MAAM;oBAACkE,EAAE,EAAE;sBAAEiC,OAAO,EAAE,cAAc;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAA7B,QAAA,eAC7C7D,OAAA,CAACL,IAAI;sBAAAoE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACTlE,OAAA,CAAChB,UAAU;oBAAC8F,OAAO,EAAC,IAAI;oBAAAjB,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNlE,OAAA,CAAChB,UAAU;kBAAC8F,OAAO,EAAC,IAAI;kBAACa,SAAS,EAAC,KAAK;kBAAA9B,QAAA,GACrC1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,CAACI,aAAa,EAAC,IAChC;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblE,OAAA,CAAChB,UAAU;kBAAC8F,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAnB,QAAA,EAAC;gBAElD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlE,OAAA,CAACf,IAAI;QAACiG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAtB,QAAA,gBACzB7D,OAAA,CAACf,IAAI;UAACmG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA1B,QAAA,eACvB7D,OAAA,CAACd,IAAI;YAAA2E,QAAA,eACH7D,OAAA,CAACb,WAAW;cAAA0E,QAAA,gBACV7D,OAAA,CAAChB,UAAU;gBAAC8F,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAlB,QAAA,EAAC;cAEtC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAAC4F,IAAI;gBAACzF,IAAI,EAAE2C,aAAc;gBAAC+C,OAAO,EAAEvD;cAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPlE,OAAA,CAACf,IAAI;UAACmG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA1B,QAAA,eACvB7D,OAAA,CAACd,IAAI;YAAA2E,QAAA,eACH7D,OAAA,CAACb,WAAW;cAAA0E,QAAA,gBACV7D,OAAA,CAAChB,UAAU;gBAAC8F,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAlB,QAAA,EAAC;cAEtC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAAC8F,QAAQ;gBAAC3F,IAAI,EAAEiD;cAAa;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAChE,EAAA,CAnSID,SAAmB;AAAA8F,EAAA,GAAnB9F,SAAmB;AAqSzB,eAAeA,SAAS;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}