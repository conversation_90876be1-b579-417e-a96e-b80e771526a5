[{"E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\index.tsx": "1", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\App.tsx": "2", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\serviceWorkerRegistration.ts": "3", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\theme\\theme.ts": "4", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\store\\store.ts": "5", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\i18n\\i18n.ts": "6", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\utils\\reportWebVitals.ts": "7", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\components\\common\\ErrorBoundary.tsx": "8", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\components\\common\\PWAInstallPrompt.tsx": "9", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\hooks\\useAuth.ts": "10", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\hooks\\useSEO.ts": "11", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\hooks\\useGeolocation.ts": "12", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\components\\common\\ProtectedRoute.tsx": "13", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\components\\common\\LoadingScreen.tsx": "14", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\components\\layout\\Layout.tsx": "15", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\pages\\Auth\\Register.tsx": "16", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\pages\\Query\\Query.tsx": "17", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\pages\\NotFound\\NotFound.tsx": "18", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\pages\\Auth\\Login.tsx": "19", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\pages\\Documents\\Documents.tsx": "20", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\pages\\Settings\\Settings.tsx": "21", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\pages\\Dashboard\\Dashboard.tsx": "22", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\hooks\\useThemeMode.ts": "23", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\hooks\\useNotifications.ts": "24", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\components\\providers\\AuthProvider.tsx": "25", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\services\\api.ts": "26", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\pages\\Analytics\\Analytics.tsx": "27", "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant Pháp Luật Thông Minh\\frontend\\src\\components\\providers\\ThemeProvider.tsx": "28"}, {"size": 5799, "mtime": 1754008827822, "results": "29", "hashOfConfig": "30"}, {"size": 10508, "mtime": 1754008327719, "results": "31", "hashOfConfig": "30"}, {"size": 3272, "mtime": 1753971656178, "results": "32", "hashOfConfig": "30"}, {"size": 1872, "mtime": 1753971416704, "results": "33", "hashOfConfig": "30"}, {"size": 8125, "mtime": 1753969609425, "results": "34", "hashOfConfig": "30"}, {"size": 8788, "mtime": 1753969658043, "results": "35", "hashOfConfig": "30"}, {"size": 476, "mtime": 1753969747515, "results": "36", "hashOfConfig": "30"}, {"size": 6774, "mtime": 1753969695028, "results": "37", "hashOfConfig": "30"}, {"size": 6790, "mtime": 1753969732761, "results": "38", "hashOfConfig": "30"}, {"size": 10978, "mtime": 1753980821315, "results": "39", "hashOfConfig": "30"}, {"size": 1673, "mtime": 1753970746252, "results": "40", "hashOfConfig": "30"}, {"size": 1441, "mtime": 1753970734502, "results": "41", "hashOfConfig": "30"}, {"size": 1073, "mtime": 1753970755761, "results": "42", "hashOfConfig": "30"}, {"size": 2992, "mtime": 1753970771488, "results": "43", "hashOfConfig": "30"}, {"size": 12003, "mtime": 1753893891874, "results": "44", "hashOfConfig": "30"}, {"size": 8091, "mtime": 1754007659012, "results": "45", "hashOfConfig": "30"}, {"size": 10471, "mtime": 1754008796431, "results": "46", "hashOfConfig": "30"}, {"size": 3165, "mtime": 1753970943352, "results": "47", "hashOfConfig": "30"}, {"size": 5336, "mtime": 1754007646160, "results": "48", "hashOfConfig": "30"}, {"size": 8669, "mtime": 1754008500530, "results": "49", "hashOfConfig": "30"}, {"size": 6974, "mtime": 1753970876206, "results": "50", "hashOfConfig": "30"}, {"size": 3906, "mtime": 1753970792461, "results": "51", "hashOfConfig": "30"}, {"size": 1683, "mtime": 1753971347982, "results": "52", "hashOfConfig": "30"}, {"size": 2137, "mtime": 1753971362756, "results": "53", "hashOfConfig": "30"}, {"size": 380, "mtime": 1753979717407, "results": "54", "hashOfConfig": "30"}, {"size": 2436, "mtime": 1754004874186, "results": "55", "hashOfConfig": "30"}, {"size": 11830, "mtime": 1754008816044, "results": "56", "hashOfConfig": "30"}, {"size": 567, "mtime": 1754008389818, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "w1zlp8", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\index.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\App.tsx", ["142"], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\serviceWorkerRegistration.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\theme\\theme.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\store\\store.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\i18n\\i18n.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\utils\\reportWebVitals.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\components\\common\\PWAInstallPrompt.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\hooks\\useAuth.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\hooks\\useSEO.ts", ["143"], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\hooks\\useGeolocation.ts", ["144"], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\components\\common\\ProtectedRoute.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\components\\common\\LoadingScreen.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\components\\layout\\Layout.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\pages\\Auth\\Register.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\pages\\Query\\Query.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\pages\\NotFound\\NotFound.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\pages\\Auth\\Login.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\pages\\Documents\\Documents.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\pages\\Settings\\Settings.tsx", ["145", "146"], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\pages\\Dashboard\\Dashboard.tsx", ["147"], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\hooks\\useThemeMode.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\hooks\\useNotifications.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\components\\providers\\AuthProvider.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\services\\api.ts", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\pages\\Analytics\\Analytics.tsx", [], [], "E:\\Project\\AI\\RAG\\Law\\LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>\\frontend\\src\\components\\providers\\ThemeProvider.tsx", [], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 64, "column": 28, "nodeType": "150", "messageId": "151", "endLine": 64, "endColumn": 36}, {"ruleId": "148", "severity": 1, "message": "152", "line": 2, "column": 10, "nodeType": "150", "messageId": "151", "endLine": 2, "endColumn": 16}, {"ruleId": "153", "severity": 1, "message": "154", "line": 66, "column": 6, "nodeType": "155", "endLine": 66, "endColumn": 8, "suggestions": "156"}, {"ruleId": "148", "severity": 1, "message": "157", "line": 12, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 12, "endColumn": 10}, {"ruleId": "148", "severity": 1, "message": "158", "line": 21, "column": 15, "nodeType": "150", "messageId": "151", "endLine": 21, "endColumn": 27}, {"ruleId": "148", "severity": 1, "message": "159", "line": 14, "column": 16, "nodeType": "150", "messageId": "151", "endLine": 14, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'geoError' is assigned a value but never used.", "Identifier", "unusedVar", "'Helmet' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'options'. Either include it or remove the dependency array.", "ArrayExpression", ["160"], "'Divider' is defined but never used.", "'LanguageIcon' is defined but never used.", "'DashboardIcon' is defined but never used.", {"desc": "161", "fix": "162"}, "Update the dependencies array to be: [options]", {"range": "163", "text": "164"}, [1416, 1418], "[options]"]