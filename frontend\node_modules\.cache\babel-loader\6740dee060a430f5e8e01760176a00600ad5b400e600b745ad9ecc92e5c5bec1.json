{"ast": null, "code": "var _jsxFileName = \"E:\\\\Project\\\\AI\\\\RAG\\\\Law\\\\LegalMind Pro - AI Assistant Ph\\xE1p Lu\\u1EADt Th\\xF4ng Minh\\\\frontend\\\\src\\\\index.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ReactQueryDevtools } from 'react-query/devtools';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { HelmetProvider } from 'react-helmet-async';\nimport { I18nextProvider } from 'react-i18next';\nimport App from './App';\nimport { store } from './store/store';\nimport i18n from './i18n/i18n';\nimport { ErrorBoundary } from './components/common/ErrorBoundary';\nimport { PWAInstallPrompt } from './components/common/PWAInstallPrompt';\nimport { AuthProvider } from './components/providers/AuthProvider';\nimport reportWebVitals from './utils/reportWebVitals';\nimport * as serviceWorkerRegistration from './serviceWorkerRegistration';\n\n// React Query client configuration\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 3,\n      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\n      staleTime: 5 * 60 * 1000,\n      // 5 minutes\n      cacheTime: 10 * 60 * 1000,\n      // 10 minutes\n      refetchOnWindowFocus: false\n    },\n    mutations: {\n      retry: 1\n    }\n  }\n});\n\n// SEO and Analytics setup\nconst setupAnalytics = () => {\n  // Google Analytics 4\n  if (process.env.REACT_APP_GA_MEASUREMENT_ID) {\n    const script = document.createElement('script');\n    script.async = true;\n    script.src = `https://www.googletagmanager.com/gtag/js?id=${process.env.REACT_APP_GA_MEASUREMENT_ID}`;\n    document.head.appendChild(script);\n    window.dataLayer = window.dataLayer || [];\n    const gtag = (...args) => {\n      window.dataLayer.push(args);\n    };\n    window.gtag = gtag;\n    gtag('js', new Date());\n    gtag('config', process.env.REACT_APP_GA_MEASUREMENT_ID, {\n      page_title: 'LegalMind Pro - AI Assistant Pháp Luật Thông Minh',\n      page_location: window.location.href,\n      custom_map: {\n        custom_parameter_1: 'legal_ai_usage',\n        custom_parameter_2: 'vietnamese_legal_queries'\n      }\n    });\n\n    // Enhanced ecommerce tracking for legal services\n    gtag('config', process.env.REACT_APP_GA_MEASUREMENT_ID, {\n      custom_map: {\n        dimension1: 'user_type',\n        dimension2: 'legal_category',\n        dimension3: 'query_complexity'\n      }\n    });\n  }\n\n  // Microsoft Clarity for user behavior analytics\n  if (process.env.REACT_APP_CLARITY_PROJECT_ID) {\n    (function (c, l, a, r, i) {\n      c[a] = c[a] || function () {\n        (c[a].q = c[a].q || []).push(arguments);\n      };\n      const t = l.createElement(r);\n      t.async = 1;\n      t.src = \"https://www.clarity.ms/tag/\" + i;\n      const y = l.getElementsByTagName(r)[0];\n      y.parentNode.insertBefore(t, y);\n    })(window, document, \"clarity\", \"script\", process.env.REACT_APP_CLARITY_PROJECT_ID);\n  }\n};\n\n// Performance monitoring\nconst setupPerformanceMonitoring = () => {\n  // Core Web Vitals tracking\n  reportWebVitals(metric => {\n    // Send to analytics\n    if (window.gtag) {\n      window.gtag('event', metric.name, {\n        event_category: 'Web Vitals',\n        event_label: metric.id,\n        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n        non_interaction: true\n      });\n    }\n\n    // Send to custom monitoring endpoint\n    if (process.env.REACT_APP_MONITORING_ENDPOINT) {\n      fetch(process.env.REACT_APP_MONITORING_ENDPOINT, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          metric: metric.name,\n          value: metric.value,\n          id: metric.id,\n          timestamp: Date.now(),\n          url: window.location.href,\n          userAgent: navigator.userAgent\n        })\n      }).catch(console.error);\n    }\n  });\n};\n\n// Initialize app\nconst initializeApp = () => {\n  setupAnalytics();\n  setupPerformanceMonitoring();\n\n  // Register service worker for PWA functionality\n  if (process.env.NODE_ENV === 'production') {\n    serviceWorkerRegistration.register({\n      onSuccess: () => {\n        console.log('LegalMind Pro is now available offline!');\n      },\n      onUpdate: registration => {\n        console.log('New version available! Please refresh to update.');\n        // Show update notification to user\n        if (window.confirm('New version available! Refresh to update?')) {\n          window.location.reload();\n        }\n      }\n    });\n  }\n};\n\n// App component with all providers\nconst AppWithProviders = () => {\n  _s();\n  React.useEffect(() => {\n    initializeApp();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(HelmetProvider, {\n      children: /*#__PURE__*/_jsxDEV(Provider, {\n        store: store,\n        children: /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n          client: queryClient,\n          children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n            children: [/*#__PURE__*/_jsxDEV(ThemeProvider, {\n              children: /*#__PURE__*/_jsxDEV(I18nextProvider, {\n                i18n: i18n,\n                children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n                  children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(PWAInstallPrompt, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(ReactQueryDevtools, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 58\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n\n// Render app\n_s(AppWithProviders, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = AppWithProviders;\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(AppWithProviders, {}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 174,\n  columnNumber: 13\n}, this));\n\n// Type declarations for global objects\n\nexport default AppWithProviders;\nvar _c;\n$RefreshReg$(_c, \"AppWithProviders\");", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Provider", "QueryClient", "QueryClientProvider", "ReactQueryDevtools", "ThemeProvider", "CssBaseline", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "I18nextProvider", "App", "store", "i18n", "Error<PERSON>ou<PERSON><PERSON>", "PWAInstallPrompt", "<PERSON>th<PERSON><PERSON><PERSON>", "reportWebVitals", "serviceWorkerRegistration", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "retry<PERSON><PERSON><PERSON>", "attemptIndex", "Math", "min", "staleTime", "cacheTime", "refetchOnWindowFocus", "mutations", "setupAnalytics", "process", "env", "REACT_APP_GA_MEASUREMENT_ID", "script", "document", "createElement", "async", "src", "head", "append<PERSON><PERSON><PERSON>", "window", "dataLayer", "gtag", "args", "push", "Date", "page_title", "page_location", "location", "href", "custom_map", "custom_parameter_1", "custom_parameter_2", "dimension1", "dimension2", "dimension3", "REACT_APP_CLARITY_PROJECT_ID", "c", "l", "a", "r", "i", "q", "arguments", "t", "y", "getElementsByTagName", "parentNode", "insertBefore", "setupPerformanceMonitoring", "metric", "name", "event_category", "event_label", "id", "value", "round", "non_interaction", "REACT_APP_MONITORING_ENDPOINT", "fetch", "method", "headers", "body", "JSON", "stringify", "timestamp", "now", "url", "userAgent", "navigator", "catch", "console", "error", "initializeApp", "NODE_ENV", "register", "onSuccess", "log", "onUpdate", "registration", "confirm", "reload", "AppWithProviders", "_s", "useEffect", "children", "client", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "root", "createRoot", "getElementById", "render", "$RefreshReg$"], "sources": ["E:/Project/AI/RAG/Law/LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ReactQueryDevtools } from 'react-query/devtools';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { HelmetProvider } from 'react-helmet-async';\nimport { I18nextProvider } from 'react-i18next';\n\nimport App from './App';\nimport { store } from './store/store';\nimport i18n from './i18n/i18n';\nimport { ErrorBoundary } from './components/common/ErrorBoundary';\nimport { PWAInstallPrompt } from './components/common/PWAInstallPrompt';\nimport { AuthProvider } from './components/providers/AuthProvider';\nimport { ThemeProvider as CustomThemeProvider } from './components/providers/ThemeProvider';\nimport reportWebVitals from './utils/reportWebVitals';\nimport * as serviceWorkerRegistration from './serviceWorkerRegistration';\n\n// React Query client configuration\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 3,\n      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      cacheTime: 10 * 60 * 1000, // 10 minutes\n      refetchOnWindowFocus: false,\n    },\n    mutations: {\n      retry: 1,\n    },\n  },\n});\n\n// SEO and Analytics setup\nconst setupAnalytics = () => {\n  // Google Analytics 4\n  if (process.env.REACT_APP_GA_MEASUREMENT_ID) {\n    const script = document.createElement('script');\n    script.async = true;\n    script.src = `https://www.googletagmanager.com/gtag/js?id=${process.env.REACT_APP_GA_MEASUREMENT_ID}`;\n    document.head.appendChild(script);\n\n    window.dataLayer = window.dataLayer || [];\n    const gtag = (...args: any[]) => {\n      window.dataLayer.push(args);\n    };\n    window.gtag = gtag;\n    gtag('js', new Date());\n    gtag('config', process.env.REACT_APP_GA_MEASUREMENT_ID, {\n      page_title: 'LegalMind Pro - AI Assistant Pháp Luật Thông Minh',\n      page_location: window.location.href,\n      custom_map: {\n        custom_parameter_1: 'legal_ai_usage',\n        custom_parameter_2: 'vietnamese_legal_queries',\n      },\n    });\n\n    // Enhanced ecommerce tracking for legal services\n    gtag('config', process.env.REACT_APP_GA_MEASUREMENT_ID, {\n      custom_map: {\n        dimension1: 'user_type',\n        dimension2: 'legal_category',\n        dimension3: 'query_complexity',\n      },\n    });\n  }\n\n  // Microsoft Clarity for user behavior analytics\n  if (process.env.REACT_APP_CLARITY_PROJECT_ID) {\n    (function(c: any, l: any, a: any, r: any, i: any) {\n      c[a] = c[a] || function() { (c[a].q = c[a].q || []).push(arguments); };\n      const t = l.createElement(r);\n      t.async = 1;\n      t.src = \"https://www.clarity.ms/tag/\" + i;\n      const y = l.getElementsByTagName(r)[0];\n      y.parentNode.insertBefore(t, y);\n    })(window, document, \"clarity\", \"script\", process.env.REACT_APP_CLARITY_PROJECT_ID);\n  }\n};\n\n// Performance monitoring\nconst setupPerformanceMonitoring = () => {\n  // Core Web Vitals tracking\n  reportWebVitals((metric) => {\n    // Send to analytics\n    if (window.gtag) {\n      window.gtag('event', metric.name, {\n        event_category: 'Web Vitals',\n        event_label: metric.id,\n        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n        non_interaction: true,\n      });\n    }\n\n    // Send to custom monitoring endpoint\n    if (process.env.REACT_APP_MONITORING_ENDPOINT) {\n      fetch(process.env.REACT_APP_MONITORING_ENDPOINT, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          metric: metric.name,\n          value: metric.value,\n          id: metric.id,\n          timestamp: Date.now(),\n          url: window.location.href,\n          userAgent: navigator.userAgent,\n        }),\n      }).catch(console.error);\n    }\n  });\n};\n\n// Initialize app\nconst initializeApp = () => {\n  setupAnalytics();\n  setupPerformanceMonitoring();\n\n  // Register service worker for PWA functionality\n  if (process.env.NODE_ENV === 'production') {\n    serviceWorkerRegistration.register({\n      onSuccess: () => {\n        console.log('LegalMind Pro is now available offline!');\n      },\n      onUpdate: (registration) => {\n        console.log('New version available! Please refresh to update.');\n        // Show update notification to user\n        if (window.confirm('New version available! Refresh to update?')) {\n          window.location.reload();\n        }\n      },\n    });\n  }\n};\n\n// App component with all providers\nconst AppWithProviders: React.FC = () => {\n  React.useEffect(() => {\n    initializeApp();\n  }, []);\n\n  return (\n    <ErrorBoundary>\n      <HelmetProvider>\n        <Provider store={store}>\n          <QueryClientProvider client={queryClient}>\n            <AuthProvider>\n              <ThemeProvider>\n                <I18nextProvider i18n={i18n}>\n                  <CssBaseline />\n                  <BrowserRouter>\n                    <App />\n                    <PWAInstallPrompt />\n                  </BrowserRouter>\n                </I18nextProvider>\n              </ThemeProvider>\n              {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}\n            </AuthProvider>\n          </QueryClientProvider>\n        </Provider>\n      </HelmetProvider>\n    </ErrorBoundary>\n  );\n};\n\n// Render app\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(<AppWithProviders />);\n\n// Type declarations for global objects\ndeclare global {\n  interface Window {\n    dataLayer: any[];\n    gtag: (...args: any[]) => void;\n    clarity: (action: string, key: string, value: any) => void;\n  }\n}\n\nexport default AppWithProviders;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,eAAe,QAAQ,eAAe;AAE/C,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,YAAY,QAAQ,qCAAqC;AAElE,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAO,KAAKC,yBAAyB,MAAM,6BAA6B;;AAExE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIjB,WAAW,CAAC;EAClCkB,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,UAAU,EAAGC,YAAY,IAAKC,IAAI,CAACC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAIF,YAAY,EAAE,KAAK,CAAC;MACvEG,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MAAE;MAC3BC,oBAAoB,EAAE;IACxB,CAAC;IACDC,SAAS,EAAE;MACTR,KAAK,EAAE;IACT;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMS,cAAc,GAAGA,CAAA,KAAM;EAC3B;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,2BAA2B,EAAE;IAC3C,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACG,KAAK,GAAG,IAAI;IACnBH,MAAM,CAACI,GAAG,GAAG,+CAA+CP,OAAO,CAACC,GAAG,CAACC,2BAA2B,EAAE;IACrGE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC;IAEjCO,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACC,SAAS,IAAI,EAAE;IACzC,MAAMC,IAAI,GAAGA,CAAC,GAAGC,IAAW,KAAK;MAC/BH,MAAM,CAACC,SAAS,CAACG,IAAI,CAACD,IAAI,CAAC;IAC7B,CAAC;IACDH,MAAM,CAACE,IAAI,GAAGA,IAAI;IAClBA,IAAI,CAAC,IAAI,EAAE,IAAIG,IAAI,CAAC,CAAC,CAAC;IACtBH,IAAI,CAAC,QAAQ,EAAEZ,OAAO,CAACC,GAAG,CAACC,2BAA2B,EAAE;MACtDc,UAAU,EAAE,mDAAmD;MAC/DC,aAAa,EAAEP,MAAM,CAACQ,QAAQ,CAACC,IAAI;MACnCC,UAAU,EAAE;QACVC,kBAAkB,EAAE,gBAAgB;QACpCC,kBAAkB,EAAE;MACtB;IACF,CAAC,CAAC;;IAEF;IACAV,IAAI,CAAC,QAAQ,EAAEZ,OAAO,CAACC,GAAG,CAACC,2BAA2B,EAAE;MACtDkB,UAAU,EAAE;QACVG,UAAU,EAAE,WAAW;QACvBC,UAAU,EAAE,gBAAgB;QAC5BC,UAAU,EAAE;MACd;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIzB,OAAO,CAACC,GAAG,CAACyB,4BAA4B,EAAE;IAC5C,CAAC,UAASC,CAAM,EAAEC,CAAM,EAAEC,CAAM,EAAEC,CAAM,EAAEC,CAAM,EAAE;MAChDJ,CAAC,CAACE,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,IAAI,YAAW;QAAE,CAACF,CAAC,CAACE,CAAC,CAAC,CAACG,CAAC,GAAGL,CAAC,CAACE,CAAC,CAAC,CAACG,CAAC,IAAI,EAAE,EAAElB,IAAI,CAACmB,SAAS,CAAC;MAAE,CAAC;MACtE,MAAMC,CAAC,GAAGN,CAAC,CAACvB,aAAa,CAACyB,CAAC,CAAC;MAC5BI,CAAC,CAAC5B,KAAK,GAAG,CAAC;MACX4B,CAAC,CAAC3B,GAAG,GAAG,6BAA6B,GAAGwB,CAAC;MACzC,MAAMI,CAAC,GAAGP,CAAC,CAACQ,oBAAoB,CAACN,CAAC,CAAC,CAAC,CAAC,CAAC;MACtCK,CAAC,CAACE,UAAU,CAACC,YAAY,CAACJ,CAAC,EAAEC,CAAC,CAAC;IACjC,CAAC,EAAEzB,MAAM,EAAEN,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAEJ,OAAO,CAACC,GAAG,CAACyB,4BAA4B,CAAC;EACrF;AACF,CAAC;;AAED;AACA,MAAMa,0BAA0B,GAAGA,CAAA,KAAM;EACvC;EACAxD,eAAe,CAAEyD,MAAM,IAAK;IAC1B;IACA,IAAI9B,MAAM,CAACE,IAAI,EAAE;MACfF,MAAM,CAACE,IAAI,CAAC,OAAO,EAAE4B,MAAM,CAACC,IAAI,EAAE;QAChCC,cAAc,EAAE,YAAY;QAC5BC,WAAW,EAAEH,MAAM,CAACI,EAAE;QACtBC,KAAK,EAAEpD,IAAI,CAACqD,KAAK,CAACN,MAAM,CAACC,IAAI,KAAK,KAAK,GAAGD,MAAM,CAACK,KAAK,GAAG,IAAI,GAAGL,MAAM,CAACK,KAAK,CAAC;QAC7EE,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/C,OAAO,CAACC,GAAG,CAAC+C,6BAA6B,EAAE;MAC7CC,KAAK,CAACjD,OAAO,CAACC,GAAG,CAAC+C,6BAA6B,EAAE;QAC/CE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBd,MAAM,EAAEA,MAAM,CAACC,IAAI;UACnBI,KAAK,EAAEL,MAAM,CAACK,KAAK;UACnBD,EAAE,EAAEJ,MAAM,CAACI,EAAE;UACbW,SAAS,EAAExC,IAAI,CAACyC,GAAG,CAAC,CAAC;UACrBC,GAAG,EAAE/C,MAAM,CAACQ,QAAQ,CAACC,IAAI;UACzBuC,SAAS,EAAEC,SAAS,CAACD;QACvB,CAAC;MACH,CAAC,CAAC,CAACE,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC;IACzB;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1BhE,cAAc,CAAC,CAAC;EAChBwC,0BAA0B,CAAC,CAAC;;EAE5B;EACA,IAAIvC,OAAO,CAACC,GAAG,CAAC+D,QAAQ,KAAK,YAAY,EAAE;IACzChF,yBAAyB,CAACiF,QAAQ,CAAC;MACjCC,SAAS,EAAEA,CAAA,KAAM;QACfL,OAAO,CAACM,GAAG,CAAC,yCAAyC,CAAC;MACxD,CAAC;MACDC,QAAQ,EAAGC,YAAY,IAAK;QAC1BR,OAAO,CAACM,GAAG,CAAC,kDAAkD,CAAC;QAC/D;QACA,IAAIzD,MAAM,CAAC4D,OAAO,CAAC,2CAA2C,CAAC,EAAE;UAC/D5D,MAAM,CAACQ,QAAQ,CAACqD,MAAM,CAAC,CAAC;QAC1B;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC3G,KAAK,CAAC4G,SAAS,CAAC,MAAM;IACpBX,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7E,OAAA,CAACN,aAAa;IAAA+F,QAAA,eACZzF,OAAA,CAACX,cAAc;MAAAoG,QAAA,eACbzF,OAAA,CAACjB,QAAQ;QAACS,KAAK,EAAEA,KAAM;QAAAiG,QAAA,eACrBzF,OAAA,CAACf,mBAAmB;UAACyG,MAAM,EAAEzF,WAAY;UAAAwF,QAAA,eACvCzF,OAAA,CAACJ,YAAY;YAAA6F,QAAA,gBACXzF,OAAA,CAACb,aAAa;cAAAsG,QAAA,eACZzF,OAAA,CAACV,eAAe;gBAACG,IAAI,EAAEA,IAAK;gBAAAgG,QAAA,gBAC1BzF,OAAA,CAACZ,WAAW;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACf9F,OAAA,CAAClB,aAAa;kBAAA2G,QAAA,gBACZzF,OAAA,CAACT,GAAG;oBAAAoG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACP9F,OAAA,CAACL,gBAAgB;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACfhF,OAAO,CAACC,GAAG,CAAC+D,QAAQ,KAAK,aAAa,iBAAI9E,OAAA,CAACd,kBAAkB;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;;AAED;AAAAP,EAAA,CA7BMD,gBAA0B;AAAAS,EAAA,GAA1BT,gBAA0B;AA8BhC,MAAMU,IAAI,GAAGnH,QAAQ,CAACoH,UAAU,CAC9B/E,QAAQ,CAACgF,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDF,IAAI,CAACG,MAAM,cAACnG,OAAA,CAACsF,gBAAgB;EAAAK,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAAC,CAAC;;AAEjC;;AASA,eAAeR,gBAAgB;AAAC,IAAAS,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}