import express from 'express';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { body, query, validationResult } from 'express-validator';
import { v4 as uuidv4 } from 'uuid';
import { AIService } from '../services/aiService';
import { QueryService } from '../services/queryService';
import { logger, logUserActivity, logPerformance } from '../utils/logger';
import { 
  incrementLegalQuery,
  observeQueryProcessingTime,
  QUERY_SATISFACTION,
  incrementSecurityEvent 
} from '../utils/metrics';

const router = express.Router();
const aiService = new AIService();
const queryService = new QueryService();

// In-memory storage for queries (replace with database in production)
const queries = new Map();

// Rate limiting for query endpoints
const queryLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 queries per minute for regular users
  message: {
    error: 'Too many queries, please slow down',
    retryAfter: '1 minute'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID if authenticated, otherwise IP
    return req.user?.id || req.ip;
  },
  skip: (req) => {
    // Premium users get higher limits
    return req.user?.role === 'premium' || req.user?.role === 'admin';
  },
  handler: (req, res) => {
    incrementSecurityEvent('rate_limit', 'query');
    res.status(429).json({
      error: 'Too many queries, please slow down',
      retryAfter: '1 minute'
    });
  },
});

// Premium user rate limiting (higher limits)
const premiumQueryLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 50, // 50 queries per minute for premium users
  message: {
    error: 'Query limit exceeded',
    retryAfter: '1 minute'
  },
  keyGenerator: (req) => req.user?.id || req.ip,
  skip: (req) => req.user?.role === 'admin',
});

// Slow down for heavy usage
const querySlowDown = slowDown({
  windowMs: 60 * 1000, // 1 minute
  delayAfter: 5, // Allow 5 requests per minute at full speed
  delayMs: 1000, // Add 1 second delay per request after delayAfter
  maxDelayMs: 10000, // Maximum delay of 10 seconds
});

// Validation middleware
const queryValidation = [
  body('query')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Query must be between 10 and 2000 characters'),
  body('queryType')
    .optional()
    .isIn(['legal_advice', 'document_search', 'case_analysis', 'regulation_lookup'])
    .withMessage('Invalid query type'),
  body('context')
    .optional()
    .isObject()
    .withMessage('Context must be an object'),
];

const feedbackValidation = [
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('feedback')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Feedback too long'),
  body('isHelpful')
    .optional()
    .isBoolean()
    .withMessage('isHelpful must be boolean'),
];

// Authentication middleware (simplified)
const authenticate = (req: any, res: any, next: any) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // In production, verify JWT token and get user
  // For now, simulate authenticated user
  req.user = {
    id: 'user-123',
    email: '<EMAIL>',
    role: 'user',
    name: 'Test User'
  };
  
  next();
};

/**
 * @swagger
 * /api/query/legal:
 *   post:
 *     summary: Submit a legal query
 *     tags: [Legal Queries]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *             properties:
 *               query:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 2000
 *               queryType:
 *                 type: string
 *                 enum: [legal_advice, document_search, case_analysis, regulation_lookup]
 *               context:
 *                 type: object
 *     responses:
 *       200:
 *         description: Query processed successfully
 *       400:
 *         description: Validation error
 *       429:
 *         description: Rate limit exceeded
 */
router.post('/legal', 
  authenticate, 
  queryLimiter, 
  premiumQueryLimiter, 
  querySlowDown, 
  queryValidation, 
  async (req, res) => {
    const startTime = Date.now();
    
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        incrementLegalQuery(req.body.queryType || 'legal_advice', false);
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { query: queryText, queryType = 'legal_advice', context } = req.body;
      const userId = req.user.id;

      logger.info('Processing legal query', {
        userId,
        queryType,
        queryLength: queryText.length,
        hasContext: !!context,
      });

      // Process query with enhanced query service (includes document search)
      const queryResponse = await queryService.processQuery({
        query: queryText,
        context,
        userId,
        queryType,
        includeDocuments: true, // Always include document search
      });

      // Store query in database
      const queryRecord = {
        id: queryResponse.queryId,
        userId,
        queryText,
        queryType,
        context: context || {},
        responseText: queryResponse.response,
        responseMetadata: queryResponse.metadata,
        sources: queryResponse.sources,
        processingTime: queryResponse.processingTime,
        tokensUsed: queryResponse.tokensUsed,
        confidence: queryResponse.confidence,
        rating: null,
        feedback: null,
        isHelpful: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      queries.set(queryResponse.queryId, queryRecord);

      const totalProcessingTime = Date.now() - startTime;

      // Record metrics
      incrementLegalQuery(queryType, true);
      observeQueryProcessingTime(queryType, totalProcessingTime / 1000);

      // Log user activity
      logUserActivity(userId, 'legal_query', 'query', {
        queryId: queryResponse.queryId,
        queryType,
        processingTime: queryResponse.processingTime,
        tokensUsed: queryResponse.tokensUsed,
        confidence: queryResponse.confidence,
        documentSourceCount: queryResponse.sources.filter(s => s.type === 'document').length,
      });

      logPerformance('processLegalQuery', queryResponse.processingTime, {
        queryId: queryResponse.queryId,
        queryType,
        tokensUsed: queryResponse.tokensUsed,
      });

      logger.info('Legal query processed successfully', {
        queryId: queryResponse.queryId,
        userId,
        queryType,
        processingTime: queryResponse.processingTime,
        tokensUsed: queryResponse.tokensUsed,
        confidence: queryResponse.confidence,
        sourceCount: queryResponse.sources.length,
        documentSourceCount: queryResponse.sources.filter(s => s.type === 'document').length,
      });

      res.json({
        queryId: queryResponse.queryId,
        response: queryResponse.response,
        sources: queryResponse.sources,
        confidence: queryResponse.confidence,
        processingTime: queryResponse.processingTime,
        tokensUsed: queryResponse.tokensUsed,
        metadata: queryResponse.metadata,
      });

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      incrementLegalQuery(req.body.queryType || 'legal_advice', false);
      
      logger.error('Legal query processing failed', {
        error: error.message,
        userId: req.user?.id,
        queryType: req.body.queryType,
        processingTime,
      });

      res.status(500).json({
        error: 'Query processing failed',
        message: 'Unable to process your query at this time. Please try again.',
        processingTime,
      });
    }
  }
);

/**
 * @swagger
 * /api/query/{queryId}/feedback:
 *   post:
 *     summary: Submit feedback for a query
 *     tags: [Legal Queries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: queryId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - rating
 *             properties:
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *               feedback:
 *                 type: string
 *               isHelpful:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Feedback submitted successfully
 *       404:
 *         description: Query not found
 */
router.post('/:queryId/feedback', 
  authenticate, 
  feedbackValidation, 
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { queryId } = req.params;
      const { rating, feedback, isHelpful } = req.body;
      const userId = req.user.id;

      const queryRecord = queries.get(queryId);
      if (!queryRecord || queryRecord.userId !== userId) {
        return res.status(404).json({
          error: 'Query not found'
        });
      }

      // Update query with feedback
      queryRecord.rating = rating;
      queryRecord.feedback = feedback || null;
      queryRecord.isHelpful = isHelpful !== undefined ? isHelpful : null;
      queryRecord.updatedAt = new Date();

      queries.set(queryId, queryRecord);

      // Record satisfaction metric
      QUERY_SATISFACTION.observe(rating);

      // Log user activity
      logUserActivity(userId, 'query_feedback', 'query', {
        queryId,
        rating,
        isHelpful,
        hasFeedback: !!feedback,
      });

      logger.info('Query feedback submitted', {
        queryId,
        userId,
        rating,
        isHelpful,
        hasFeedback: !!feedback,
      });

      res.json({
        message: 'Feedback submitted successfully',
        queryId,
        rating,
        isHelpful,
      });

    } catch (error) {
      logger.error('Failed to submit query feedback', {
        error: error.message,
        queryId: req.params.queryId,
        userId: req.user?.id,
      });

      res.status(500).json({
        error: 'Failed to submit feedback'
      });
    }
  }
);

/**
 * @swagger
 * /api/query/history:
 *   get:
 *     summary: Get user's query history
 *     tags: [Legal Queries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: queryType
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Query history retrieved successfully
 */
router.get('/history', 
  authenticate, 
  [
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('queryType').optional().isIn(['legal_advice', 'document_search', 'case_analysis', 'regulation_lookup']),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const userId = req.user.id;
      const page = req.query.page as number || 1;
      const limit = req.query.limit as number || 20;
      const queryType = req.query.queryType as string;

      // Filter user's queries
      let userQueries = Array.from(queries.values())
        .filter(q => q.userId === userId);

      if (queryType) {
        userQueries = userQueries.filter(q => q.queryType === queryType);
      }

      // Sort by creation date (newest first)
      userQueries.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      // Paginate
      const total = userQueries.length;
      const totalPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;
      const paginatedQueries = userQueries.slice(offset, offset + limit);

      // Remove sensitive data
      const sanitizedQueries = paginatedQueries.map(q => ({
        id: q.id,
        queryText: q.queryText,
        queryType: q.queryType,
        responseText: q.responseText,
        sources: q.sources,
        confidence: q.confidence,
        rating: q.rating,
        isHelpful: q.isHelpful,
        processingTime: q.processingTime,
        tokensUsed: q.tokensUsed,
        createdAt: q.createdAt,
      }));

      res.json({
        queries: sanitizedQueries,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      });

    } catch (error) {
      logger.error('Failed to get query history', {
        error: error.message,
        userId: req.user?.id,
      });

      res.status(500).json({
        error: 'Failed to retrieve query history'
      });
    }
  }
);

export default router;
