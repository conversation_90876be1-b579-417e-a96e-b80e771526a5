{"ast": null, "code": "var _jsxFileName = \"E:\\\\Project\\\\AI\\\\RAG\\\\Law\\\\LegalMind Pro - AI Assistant Ph\\xE1p Lu\\u1EADt Th\\xF4ng Minh\\\\frontend\\\\src\\\\components\\\\providers\\\\ThemeProvider.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';\nimport { useThemeMode } from '../../hooks/useThemeMode';\nimport { themeConfig } from '../../theme/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s();\n  const {\n    isDark\n  } = useThemeMode();\n  const currentTheme = isDark ? themeConfig.dark : themeConfig.light;\n  return /*#__PURE__*/_jsxDEV(MuiThemeProvider, {\n    theme: currentTheme,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeProvider, \"ZsY1SlRhY4opNkr6ntshQRa37mY=\", false, function () {\n  return [useThemeMode];\n});\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "ThemeProvider", "MuiThemeProvider", "useThemeMode", "themeConfig", "jsxDEV", "_jsxDEV", "children", "_s", "isDark", "currentTheme", "dark", "light", "theme", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Project/AI/RAG/Law/LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>/frontend/src/components/providers/ThemeProvider.tsx"], "sourcesContent": ["import React from 'react';\nimport { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';\nimport { useThemeMode } from '../../hooks/useThemeMode';\nimport { themeConfig } from '../../theme/theme';\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const { isDark } = useThemeMode();\n  \n  const currentTheme = isDark ? themeConfig.dark : themeConfig.light;\n  \n  return (\n    <MuiThemeProvider theme={currentTheme}>\n      {children}\n    </MuiThemeProvider>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,gBAAgB,QAAQ,sBAAsB;AACxE,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMhD,OAAO,MAAML,aAA2C,GAAGA,CAAC;EAAEM;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM;IAAEC;EAAO,CAAC,GAAGN,YAAY,CAAC,CAAC;EAEjC,MAAMO,YAAY,GAAGD,MAAM,GAAGL,WAAW,CAACO,IAAI,GAAGP,WAAW,CAACQ,KAAK;EAElE,oBACEN,OAAA,CAACJ,gBAAgB;IAACW,KAAK,EAAEH,YAAa;IAAAH,QAAA,EACnCA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEvB,CAAC;AAACT,EAAA,CAVWP,aAA2C;EAAA,QACnCE,YAAY;AAAA;AAAAe,EAAA,GADpBjB,aAA2C;AAAA,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}