# Installation
> `npm install --save @types/chart.js`

# Summary
This package contains type definitions for chart.js (https://github.com/nnnick/Chart.js).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chart.js.

### Additional Details
 * Last updated: Mon, 20 Nov 2023 23:36:23 GMT
 * Dependencies: [moment](https://npmjs.com/package/moment)

# Credits
These definitions were written by [<PERSON>](https://github.com/anuti), [<PERSON>abi<PERSON> Lavocat](https://github.com/FabienLavocat), [KentarouTakeda](https://github.com/KentarouTakeda), [<PERSON>](https://github.com/larrybahr), [<PERSON>](https://github.com/mernen), [<PERSON>](https://github.com/josefpaij), [<PERSON>](https://github.com/dan<PERSON>), [<PERSON>](https://github.com/guillau<PERSON>-ro-fr), [<PERSON>](https://github.com/archy-bold), [<PERSON>](https://github.com/braincore), [<PERSON>and<PERSON> Dorodoulis](https://github.com/alexdor), [<PERSON> Heidrich](https://github.com/mahnuh), [Conrad Holtzhausen](https://github.com/Conrad777), [Adrián Caballero](https://github.com/adripanico), [wertzui](https://github.com/wertzui), [Martin Trobäck](https://github.com/lekoaf), [Elian Cordoba](https://github.com/ElianCordoba), [Takuya Uehara](https://github.com/indigolain), [Ricardo Mello](https://github.com/ricmello), [Ray Nicholus](https://github.com/rnicholus), [Oscar Cabrera](https://github.com/mrjack88), [Carlos Anoceto](https://github.com/canoceto), [Nobuhiko Futagami](https://github.com/nobu222), [Marco Ru](https://github.com/Marcoru97), [Tony Liu](https://github.com/tonybadguy), [Mathias Helminger](https://github.com/Ilmarinen100), [Mostafa Sameti](https://github.com/IVIosi), and [Samar Mohan](https://github.com/samarmohan).
