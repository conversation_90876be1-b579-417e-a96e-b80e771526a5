{"ast": null, "code": "var _jsxFileName = \"E:\\\\Project\\\\AI\\\\RAG\\\\Law\\\\LegalMind Pro - AI Assistant Ph\\xE1p Lu\\u1EADt Th\\xF4ng Minh\\\\frontend\\\\src\\\\pages\\\\Query\\\\Query.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, TextField, Button, Paper, Card, CardContent, Chip, CircularProgress, Divider, IconButton } from '@mui/material';\nimport { Send as SendIcon, Psychology as AIIcon, History as HistoryIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon } from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Query = () => {\n  _s();\n  const [query, setQuery] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [expandedResults, setExpandedResults] = useState(new Set());\n  const [results, setResults] = useState([{\n    id: '1',\n    question: '<PERSON><PERSON><PERSON><PERSON> kiện thành lập doanh nghiệp tư nhân là gì?',\n    answer: 'Theo Luật Doanh nghiệp 2020, điều kiện thành lập doanh nghiệp tư nhân bao gồm: 1) Chủ doanh nghiệp tư nhân phải là cá nhân có đủ năng lực hành vi dân sự; 2) Không thuộc đối tượng bị cấm thành lập doanh nghiệp; 3) Có địa chỉ trụ sở chính tại Việt Nam. Ngoài ra, chủ doanh nghiệp tư nhân cần có vốn pháp định tối thiểu theo quy định của pháp luật chuyên ngành (nếu có) và phải đăng ký kinh doanh theo đúng thủ tục.',\n    timestamp: '2024-01-15 10:30',\n    confidence: 0.95,\n    sources: ['Luật Doanh nghiệp 2020', 'Nghị định 01/2021/NĐ-CP']\n  }, {\n    id: '2',\n    question: 'Thủ tục ly hôn đơn phương như thế nào?',\n    answer: 'Ly hôn đơn phương là trường hợp chỉ có một bên vợ hoặc chồng yêu cầu ly hôn. Theo Bộ luật Dân sự 2015, thủ tục ly hôn đơn phương bao gồm: 1) Nộp đơn khởi kiện lên Tòa án có thẩm quyền; 2) Tòa án tiến hành hòa giải; 3) Nếu hòa giải không thành, Tòa án sẽ xét xử và ra quyết định ly hôn.',\n    timestamp: '2024-01-14 15:20',\n    confidence: 0.88,\n    sources: ['Bộ luật Dân sự 2015', 'Luật Hôn nhân và Gia đình 2014']\n  }]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!query.trim()) return;\n    setLoading(true);\n\n    // Simulate API call\n    setTimeout(() => {\n      const newResult = {\n        id: Date.now().toString(),\n        question: query,\n        answer: 'Đây là câu trả lời mẫu từ AI. Trong thực tế, đây sẽ là phản hồi từ hệ thống AI phân tích tài liệu pháp luật.',\n        timestamp: new Date().toLocaleString('vi-VN'),\n        confidence: 0.88,\n        sources: ['Tài liệu liên quan 1', 'Tài liệu liên quan 2']\n      };\n      setResults([newResult, ...results]);\n      setQuery('');\n      setLoading(false);\n    }, 2000);\n  };\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.9) return 'success';\n    if (confidence >= 0.7) return 'warning';\n    return 'error';\n  };\n  const toggleExpanded = resultId => {\n    const newExpanded = new Set(expandedResults);\n    if (newExpanded.has(resultId)) {\n      newExpanded.delete(resultId);\n    } else {\n      newExpanded.add(resultId);\n    }\n    setExpandedResults(newExpanded);\n  };\n  const truncateText = (text, maxLength = 200) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Truy v\\u1EA5n AI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3,\n          backgroundColor: 'info.light',\n          color: 'info.contrastText'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\uD83E\\uDD16 LegalMind Pro ho\\u1EA1t \\u0111\\u1ED9ng nh\\u01B0 th\\u1EBF n\\xE0o?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 2\n          },\n          children: \"LegalMind Pro l\\xE0 tr\\u1EE3 l\\xFD AI ph\\xE1p lu\\u1EADt th\\xF4ng minh c\\xF3 kh\\u1EA3 n\\u0103ng:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ml: 2,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"div\",\n            children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Ph\\xE2n t\\xEDch t\\xE0i li\\u1EC7u \\u0111\\xE3 t\\u1EA3i l\\xEAn:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this), \" Tr\\u1EA3 l\\u1EDDi c\\xE2u h\\u1ECFi d\\u1EF1a tr\\xEAn c\\xE1c v\\u0103n b\\u1EA3n ph\\xE1p lu\\u1EADt, h\\u1EE3p \\u0111\\u1ED3ng, quy\\u1EBFt \\u0111\\u1ECBnh m\\xE0 b\\u1EA1n \\u0111\\xE3 upload\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"div\",\n            children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"T\\u01B0 v\\u1EA5n ph\\xE1p lu\\u1EADt t\\u1ED5ng qu\\xE1t:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), \" Cung c\\u1EA5p th\\xF4ng tin v\\u1EC1 ph\\xE1p lu\\u1EADt Vi\\u1EC7t Nam t\\u1EEB c\\u01A1 s\\u1EDF d\\u1EEF li\\u1EC7u \\u0111\\u01B0\\u1EE3c c\\u1EADp nh\\u1EADt\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"div\",\n            children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Ph\\xE2n t\\xEDch v\\xE0 so s\\xE1nh:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), \" \\u0110\\u1ED1i chi\\u1EBFu c\\xE1c quy \\u0111\\u1ECBnh ph\\xE1p lu\\u1EADt v\\u1EDBi t\\xECnh hu\\u1ED1ng c\\u1EE5 th\\u1EC3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontStyle: 'italic'\n          },\n          children: \"\\uD83D\\uDCA1 \\u0110\\u1EC3 c\\xF3 c\\xE2u tr\\u1EA3 l\\u1EDDi ch\\xEDnh x\\xE1c nh\\u1EA5t, h\\xE3y t\\u1EA3i l\\xEAn c\\xE1c t\\xE0i li\\u1EC7u li\\xEAn quan trong m\\u1EE5c \\\"T\\xE0i li\\u1EC7u\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\u0110\\u1EB7t c\\xE2u h\\u1ECFi ph\\xE1p lu\\u1EADt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            multiline: true,\n            rows: 4,\n            variant: \"outlined\",\n            placeholder: \"Nh\\u1EADp c\\xE2u h\\u1ECFi c\\u1EE7a b\\u1EA1n v\\u1EC1 ph\\xE1p lu\\u1EADt...\",\n            value: query,\n            onChange: e => setQuery(e.target.value),\n            disabled: loading,\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'flex-end'\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              variant: \"contained\",\n              startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 38\n              }, this) : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 71\n              }, this),\n              disabled: loading || !query.trim(),\n              size: \"large\",\n              children: loading ? 'Đang xử lý...' : 'Gửi câu hỏi'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(HistoryIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), \"L\\u1ECBch s\\u1EED truy v\\u1EA5n\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), results.map((result, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: index * 0.1\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              mb: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  sx: {\n                    flex: 1\n                  },\n                  children: result.question\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => toggleExpanded(result.id),\n                  size: \"small\",\n                  children: expandedResults.has(result.id) ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 78\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(AIIcon, {\n                  color: \"primary\",\n                  sx: {\n                    mr: 1,\n                    mt: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: expandedResults.has(result.id) ? result.answer : truncateText(result.answer)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 23\n                  }, this), result.answer.length > 200 && !expandedResults.has(result.id) && /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    onClick: () => toggleExpanded(result.id),\n                    sx: {\n                      mt: 1,\n                      p: 0,\n                      minWidth: 'auto'\n                    },\n                    children: \"Xem th\\xEAm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 1,\n                  alignItems: 'center',\n                  mt: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  label: `Độ tin cậy: ${(result.confidence * 100).toFixed(0)}%`,\n                  color: getConfidenceColor(result.confidence),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"textSecondary\",\n                  children: result.timestamp\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), result.sources.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Ngu\\u1ED3n tham kh\\u1EA3o:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 1\n                  },\n                  children: result.sources.map((source, idx) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: source,\n                    variant: \"outlined\",\n                    size: \"small\"\n                  }, idx, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)\n        }, result.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)), results.length === 0 && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 4,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"textSecondary\",\n            children: \"Ch\\u01B0a c\\xF3 truy v\\u1EA5n n\\xE0o. H\\xE3y b\\u1EAFt \\u0111\\u1EA7u b\\u1EB1ng c\\xE1ch \\u0111\\u1EB7t c\\xE2u h\\u1ECFi \\u0111\\u1EA7u ti\\xEAn.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(Query, \"pPxnEfL5OD4VAcEkxmls+mRim+8=\");\n_c = Query;\nexport default Query;\nvar _c;\n$RefreshReg$(_c, \"Query\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "CircularProgress", "Divider", "IconButton", "Send", "SendIcon", "Psychology", "AIIcon", "History", "HistoryIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "motion", "jsxDEV", "_jsxDEV", "Query", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "expandedResults", "setExpandedResults", "Set", "results", "setResults", "id", "question", "answer", "timestamp", "confidence", "sources", "handleSubmit", "e", "preventDefault", "trim", "setTimeout", "newResult", "Date", "now", "toString", "toLocaleString", "getConfidenceColor", "toggleExpanded", "resultId", "newExpanded", "has", "delete", "add", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "sx", "p", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "backgroundColor", "color", "ml", "component", "fontStyle", "onSubmit", "fullWidth", "multiline", "rows", "placeholder", "value", "onChange", "target", "disabled", "display", "justifyContent", "type", "startIcon", "size", "alignItems", "mr", "map", "result", "index", "delay", "flex", "onClick", "my", "mt", "lineHeight", "min<PERSON><PERSON><PERSON>", "flexWrap", "gap", "label", "toFixed", "source", "idx", "textAlign", "_c", "$RefreshReg$"], "sources": ["E:/Project/AI/RAG/Law/LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>/frontend/src/pages/Query/Query.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Card,\n  CardContent,\n  Chip,\n  CircularProgress,\n  Divider,\n  Collapse,\n  IconButton,\n} from '@mui/material';\nimport {\n  Send as SendIcon,\n  Psychology as AIIcon,\n  History as HistoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\n\ninterface QueryResult {\n  id: string;\n  question: string;\n  answer: string;\n  timestamp: string;\n  confidence: number;\n  sources: string[];\n}\n\nconst Query: React.FC = () => {\n  const [query, setQuery] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [expandedResults, setExpandedResults] = useState<Set<string>>(new Set());\n  const [results, setResults] = useState<QueryResult[]>([\n    {\n      id: '1',\n      question: '<PERSON><PERSON><PERSON><PERSON> kiện thành lập do<PERSON>h nghi<PERSON><PERSON> tư <PERSON>hân là gì?',\n      answer: '<PERSON> 2020, đi<PERSON><PERSON> ki<PERSON><PERSON> thành lập do<PERSON>h nghiệp tư nhân bao gồm: 1) Chủ doanh nghiệp tư nhân phải là cá nhân có đủ năng lực hành vi dân sự; 2) Không thuộc đối tượng bị cấm thành lập doanh nghiệp; 3) Có địa chỉ trụ sở chính tại Việt Nam. Ngoài ra, chủ doanh nghiệp tư nhân cần có vốn pháp định tối thiểu theo quy định của pháp luật chuyên ngành (nếu có) và phải đăng ký kinh doanh theo đúng thủ tục.',\n      timestamp: '2024-01-15 10:30',\n      confidence: 0.95,\n      sources: ['Luật Doanh nghiệp 2020', 'Nghị định 01/2021/NĐ-CP'],\n    },\n    {\n      id: '2',\n      question: 'Thủ tục ly hôn đơn phương như thế nào?',\n      answer: 'Ly hôn đơn phương là trường hợp chỉ có một bên vợ hoặc chồng yêu cầu ly hôn. Theo Bộ luật Dân sự 2015, thủ tục ly hôn đơn phương bao gồm: 1) Nộp đơn khởi kiện lên Tòa án có thẩm quyền; 2) Tòa án tiến hành hòa giải; 3) Nếu hòa giải không thành, Tòa án sẽ xét xử và ra quyết định ly hôn.',\n      timestamp: '2024-01-14 15:20',\n      confidence: 0.88,\n      sources: ['Bộ luật Dân sự 2015', 'Luật Hôn nhân và Gia đình 2014'],\n    },\n  ]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!query.trim()) return;\n\n    setLoading(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      const newResult: QueryResult = {\n        id: Date.now().toString(),\n        question: query,\n        answer: 'Đây là câu trả lời mẫu từ AI. Trong thực tế, đây sẽ là phản hồi từ hệ thống AI phân tích tài liệu pháp luật.',\n        timestamp: new Date().toLocaleString('vi-VN'),\n        confidence: 0.88,\n        sources: ['Tài liệu liên quan 1', 'Tài liệu liên quan 2'],\n      };\n      \n      setResults([newResult, ...results]);\n      setQuery('');\n      setLoading(false);\n    }, 2000);\n  };\n\n  const getConfidenceColor = (confidence: number) => {\n    if (confidence >= 0.9) return 'success';\n    if (confidence >= 0.7) return 'warning';\n    return 'error';\n  };\n\n  const toggleExpanded = (resultId: string) => {\n    const newExpanded = new Set(expandedResults);\n    if (newExpanded.has(resultId)) {\n      newExpanded.delete(resultId);\n    } else {\n      newExpanded.add(resultId);\n    }\n    setExpandedResults(newExpanded);\n  };\n\n  const truncateText = (text: string, maxLength: number = 200) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <Typography variant=\"h4\" gutterBottom>\n          Truy vấn AI\n        </Typography>\n        <Paper sx={{ p: 3, mb: 3, backgroundColor: 'info.light', color: 'info.contrastText' }}>\n          <Typography variant=\"h6\" gutterBottom>\n            🤖 LegalMind Pro hoạt động như thế nào?\n          </Typography>\n          <Typography variant=\"body2\" sx={{ mb: 2 }}>\n            LegalMind Pro là trợ lý AI pháp luật thông minh có khả năng:\n          </Typography>\n          <Box sx={{ ml: 2, mb: 2 }}>\n            <Typography variant=\"body2\" component=\"div\">\n              • <strong>Phân tích tài liệu đã tải lên:</strong> Trả lời câu hỏi dựa trên các văn bản pháp luật, hợp đồng, quyết định mà bạn đã upload\n            </Typography>\n            <Typography variant=\"body2\" component=\"div\">\n              • <strong>Tư vấn pháp luật tổng quát:</strong> Cung cấp thông tin về pháp luật Việt Nam từ cơ sở dữ liệu được cập nhật\n            </Typography>\n            <Typography variant=\"body2\" component=\"div\">\n              • <strong>Phân tích và so sánh:</strong> Đối chiếu các quy định pháp luật với tình huống cụ thể\n            </Typography>\n          </Box>\n          <Typography variant=\"body2\" sx={{ fontStyle: 'italic' }}>\n            💡 Để có câu trả lời chính xác nhất, hãy tải lên các tài liệu liên quan trong mục \"Tài liệu\"\n          </Typography>\n        </Paper>\n\n        {/* Query Input */}\n        <Paper sx={{ p: 3, mb: 4 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Đặt câu hỏi pháp luật\n          </Typography>\n          <form onSubmit={handleSubmit}>\n            <TextField\n              fullWidth\n              multiline\n              rows={4}\n              variant=\"outlined\"\n              placeholder=\"Nhập câu hỏi của bạn về pháp luật...\"\n              value={query}\n              onChange={(e) => setQuery(e.target.value)}\n              disabled={loading}\n              sx={{ mb: 2 }}\n            />\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}\n                disabled={loading || !query.trim()}\n                size=\"large\"\n              >\n                {loading ? 'Đang xử lý...' : 'Gửi câu hỏi'}\n              </Button>\n            </Box>\n          </form>\n        </Paper>\n\n        {/* Results */}\n        <Box>\n          <Typography variant=\"h5\" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>\n            <HistoryIcon sx={{ mr: 1 }} />\n            Lịch sử truy vấn\n          </Typography>\n          \n          {results.map((result, index) => (\n            <motion.div\n              key={result.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n            >\n              <Card sx={{ mb: 3 }}>\n                <CardContent>\n                  {/* Question */}\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n                    <Typography variant=\"h6\" gutterBottom sx={{ flex: 1 }}>\n                      {result.question}\n                    </Typography>\n                    <IconButton\n                      onClick={() => toggleExpanded(result.id)}\n                      size=\"small\"\n                    >\n                      {expandedResults.has(result.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Box>\n\n                  <Divider sx={{ my: 2 }} />\n\n                  {/* Answer */}\n                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>\n                    <AIIcon color=\"primary\" sx={{ mr: 1, mt: 0.5 }} />\n                    <Box sx={{ flex: 1 }}>\n                      <Typography variant=\"body1\" sx={{ lineHeight: 1.6 }}>\n                        {expandedResults.has(result.id)\n                          ? result.answer\n                          : truncateText(result.answer)\n                        }\n                      </Typography>\n                      {result.answer.length > 200 && !expandedResults.has(result.id) && (\n                        <Button\n                          size=\"small\"\n                          onClick={() => toggleExpanded(result.id)}\n                          sx={{ mt: 1, p: 0, minWidth: 'auto' }}\n                        >\n                          Xem thêm\n                        </Button>\n                      )}\n                    </Box>\n                  </Box>\n                  \n                  {/* Metadata */}\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mt: 2 }}>\n                    <Chip\n                      label={`Độ tin cậy: ${(result.confidence * 100).toFixed(0)}%`}\n                      color={getConfidenceColor(result.confidence)}\n                      size=\"small\"\n                    />\n                    <Typography variant=\"caption\" color=\"textSecondary\">\n                      {result.timestamp}\n                    </Typography>\n                  </Box>\n                  \n                  {/* Sources */}\n                  {result.sources.length > 0 && (\n                    <Box sx={{ mt: 2 }}>\n                      <Typography variant=\"subtitle2\" gutterBottom>\n                        Nguồn tham khảo:\n                      </Typography>\n                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                        {result.sources.map((source, idx) => (\n                          <Chip\n                            key={idx}\n                            label={source}\n                            variant=\"outlined\"\n                            size=\"small\"\n                          />\n                        ))}\n                      </Box>\n                    </Box>\n                  )}\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n          \n          {results.length === 0 && (\n            <Paper sx={{ p: 4, textAlign: 'center' }}>\n              <Typography variant=\"body1\" color=\"textSecondary\">\n                Chưa có truy vấn nào. Hãy bắt đầu bằng cách đặt câu hỏi đầu tiên.\n              </Typography>\n            </Paper>\n          )}\n        </Box>\n      </motion.div>\n    </Box>\n  );\n};\n\nexport default Query;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,gBAAgB,EAChBC,OAAO,EAEPC,UAAU,QACL,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,MAAM,EACpBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWvC,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAc,IAAIiC,GAAG,CAAC,CAAC,CAAC;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAgB,CACpD;IACEoC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,iDAAiD;IAC3DC,MAAM,EAAE,8ZAA8Z;IACtaC,SAAS,EAAE,kBAAkB;IAC7BC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,CAAC,wBAAwB,EAAE,yBAAyB;EAC/D,CAAC,EACD;IACEL,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,wCAAwC;IAClDC,MAAM,EAAE,+RAA+R;IACvSC,SAAS,EAAE,kBAAkB;IAC7BC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,CAAC,qBAAqB,EAAE,gCAAgC;EACnE,CAAC,CACF,CAAC;EAEF,MAAMC,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACjB,KAAK,CAACkB,IAAI,CAAC,CAAC,EAAE;IAEnBf,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACAgB,UAAU,CAAC,MAAM;MACf,MAAMC,SAAsB,GAAG;QAC7BX,EAAE,EAAEY,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBb,QAAQ,EAAEV,KAAK;QACfW,MAAM,EAAE,8GAA8G;QACtHC,SAAS,EAAE,IAAIS,IAAI,CAAC,CAAC,CAACG,cAAc,CAAC,OAAO,CAAC;QAC7CX,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,CAAC,sBAAsB,EAAE,sBAAsB;MAC1D,CAAC;MAEDN,UAAU,CAAC,CAACY,SAAS,EAAE,GAAGb,OAAO,CAAC,CAAC;MACnCN,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMsB,kBAAkB,GAAIZ,UAAkB,IAAK;IACjD,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,OAAO;EAChB,CAAC;EAED,MAAMa,cAAc,GAAIC,QAAgB,IAAK;IAC3C,MAAMC,WAAW,GAAG,IAAItB,GAAG,CAACF,eAAe,CAAC;IAC5C,IAAIwB,WAAW,CAACC,GAAG,CAACF,QAAQ,CAAC,EAAE;MAC7BC,WAAW,CAACE,MAAM,CAACH,QAAQ,CAAC;IAC9B,CAAC,MAAM;MACLC,WAAW,CAACG,GAAG,CAACJ,QAAQ,CAAC;IAC3B;IACAtB,kBAAkB,CAACuB,WAAW,CAAC;EACjC,CAAC;EAED,MAAMI,YAAY,GAAGA,CAACC,IAAY,EAAEC,SAAiB,GAAG,GAAG,KAAK;IAC9D,IAAID,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EAED,oBACErC,OAAA,CAACvB,GAAG;IAAC+D,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,eAChB1C,OAAA,CAACF,MAAM,CAAC6C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAP,QAAA,gBAE9B1C,OAAA,CAACtB,UAAU;QAACwE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAT,QAAA,EAAC;MAEtC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvD,OAAA,CAACnB,KAAK;QAAC2D,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEe,EAAE,EAAE,CAAC;UAAEC,eAAe,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAoB,CAAE;QAAAhB,QAAA,gBACpF1C,OAAA,CAACtB,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAT,QAAA,EAAC;QAEtC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvD,OAAA,CAACtB,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EAAC;QAE3C;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvD,OAAA,CAACvB,GAAG;UAAC+D,EAAE,EAAE;YAAEmB,EAAE,EAAE,CAAC;YAAEH,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACxB1C,OAAA,CAACtB,UAAU;YAACwE,OAAO,EAAC,OAAO;YAACU,SAAS,EAAC,KAAK;YAAAlB,QAAA,GAAC,SACxC,eAAA1C,OAAA;cAAA0C,QAAA,EAAQ;YAA8B;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uLACnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvD,OAAA,CAACtB,UAAU;YAACwE,OAAO,EAAC,OAAO;YAACU,SAAS,EAAC,KAAK;YAAAlB,QAAA,GAAC,SACxC,eAAA1C,OAAA;cAAA0C,QAAA,EAAQ;YAA2B;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wJAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvD,OAAA,CAACtB,UAAU;YAACwE,OAAO,EAAC,OAAO;YAACU,SAAS,EAAC,KAAK;YAAAlB,QAAA,GAAC,SACxC,eAAA1C,OAAA;cAAA0C,QAAA,EAAQ;YAAqB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,sHAC1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNvD,OAAA,CAACtB,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAEqB,SAAS,EAAE;UAAS,CAAE;UAAAnB,QAAA,EAAC;QAEzD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGRvD,OAAA,CAACnB,KAAK;QAAC2D,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEe,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,gBACzB1C,OAAA,CAACtB,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAT,QAAA,EAAC;QAEtC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvD,OAAA;UAAM8D,QAAQ,EAAE5C,YAAa;UAAAwB,QAAA,gBAC3B1C,OAAA,CAACrB,SAAS;YACRoF,SAAS;YACTC,SAAS;YACTC,IAAI,EAAE,CAAE;YACRf,OAAO,EAAC,UAAU;YAClBgB,WAAW,EAAC,0EAAsC;YAClDC,KAAK,EAAEhE,KAAM;YACbiE,QAAQ,EAAGjD,CAAC,IAAKf,QAAQ,CAACe,CAAC,CAACkD,MAAM,CAACF,KAAK,CAAE;YAC1CG,QAAQ,EAAEjE,OAAQ;YAClBmC,EAAE,EAAE;cAAEgB,EAAE,EAAE;YAAE;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACFvD,OAAA,CAACvB,GAAG;YAAC+D,EAAE,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE;YAAW,CAAE;YAAA9B,QAAA,eACvD1C,OAAA,CAACpB,MAAM;cACL6F,IAAI,EAAC,QAAQ;cACbvB,OAAO,EAAC,WAAW;cACnBwB,SAAS,EAAErE,OAAO,gBAAGL,OAAA,CAACf,gBAAgB;gBAAC0F,IAAI,EAAE;cAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACX,QAAQ;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnEe,QAAQ,EAAEjE,OAAO,IAAI,CAACF,KAAK,CAACkB,IAAI,CAAC,CAAE;cACnCsD,IAAI,EAAC,OAAO;cAAAjC,QAAA,EAEXrC,OAAO,GAAG,eAAe,GAAG;YAAa;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRvD,OAAA,CAACvB,GAAG;QAAAiE,QAAA,gBACF1C,OAAA,CAACtB,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACC,YAAY;UAACX,EAAE,EAAE;YAAE+B,OAAO,EAAE,MAAM;YAAEK,UAAU,EAAE;UAAS,CAAE;UAAAlC,QAAA,gBAClF1C,OAAA,CAACP,WAAW;YAAC+C,EAAE,EAAE;cAAEqC,EAAE,EAAE;YAAE;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mCAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ7C,OAAO,CAACoE,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBhF,OAAA,CAACF,MAAM,CAAC6C,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEgC,KAAK,EAAED,KAAK,GAAG;UAAI,CAAE;UAAAtC,QAAA,eAElD1C,OAAA,CAAClB,IAAI;YAAC0D,EAAE,EAAE;cAAEgB,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,eAClB1C,OAAA,CAACjB,WAAW;cAAA2D,QAAA,gBAEV1C,OAAA,CAACvB,GAAG;gBAAC+D,EAAE,EAAE;kBAAE+B,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEI,UAAU,EAAE;gBAAa,CAAE;gBAAAlC,QAAA,gBACtF1C,OAAA,CAACtB,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAACX,EAAE,EAAE;oBAAE0C,IAAI,EAAE;kBAAE,CAAE;kBAAAxC,QAAA,EACnDqC,MAAM,CAAClE;gBAAQ;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACbvD,OAAA,CAACb,UAAU;kBACTgG,OAAO,EAAEA,CAAA,KAAMtD,cAAc,CAACkD,MAAM,CAACnE,EAAE,CAAE;kBACzC+D,IAAI,EAAC,OAAO;kBAAAjC,QAAA,EAEXnC,eAAe,CAACyB,GAAG,CAAC+C,MAAM,CAACnE,EAAE,CAAC,gBAAGZ,OAAA,CAACH,cAAc;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACL,cAAc;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENvD,OAAA,CAACd,OAAO;gBAACsD,EAAE,EAAE;kBAAE4C,EAAE,EAAE;gBAAE;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAG1BvD,OAAA,CAACvB,GAAG;gBAAC+D,EAAE,EAAE;kBAAE+B,OAAO,EAAE,MAAM;kBAAEK,UAAU,EAAE,YAAY;kBAAEpB,EAAE,EAAE;gBAAE,CAAE;gBAAAd,QAAA,gBAC5D1C,OAAA,CAACT,MAAM;kBAACmE,KAAK,EAAC,SAAS;kBAAClB,EAAE,EAAE;oBAAEqC,EAAE,EAAE,CAAC;oBAAEQ,EAAE,EAAE;kBAAI;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClDvD,OAAA,CAACvB,GAAG;kBAAC+D,EAAE,EAAE;oBAAE0C,IAAI,EAAE;kBAAE,CAAE;kBAAAxC,QAAA,gBACnB1C,OAAA,CAACtB,UAAU;oBAACwE,OAAO,EAAC,OAAO;oBAACV,EAAE,EAAE;sBAAE8C,UAAU,EAAE;oBAAI,CAAE;oBAAA5C,QAAA,EACjDnC,eAAe,CAACyB,GAAG,CAAC+C,MAAM,CAACnE,EAAE,CAAC,GAC3BmE,MAAM,CAACjE,MAAM,GACbqB,YAAY,CAAC4C,MAAM,CAACjE,MAAM;kBAAC;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAErB,CAAC,EACZwB,MAAM,CAACjE,MAAM,CAACwB,MAAM,GAAG,GAAG,IAAI,CAAC/B,eAAe,CAACyB,GAAG,CAAC+C,MAAM,CAACnE,EAAE,CAAC,iBAC5DZ,OAAA,CAACpB,MAAM;oBACL+F,IAAI,EAAC,OAAO;oBACZQ,OAAO,EAAEA,CAAA,KAAMtD,cAAc,CAACkD,MAAM,CAACnE,EAAE,CAAE;oBACzC4B,EAAE,EAAE;sBAAE6C,EAAE,EAAE,CAAC;sBAAE5C,CAAC,EAAE,CAAC;sBAAE8C,QAAQ,EAAE;oBAAO,CAAE;oBAAA7C,QAAA,EACvC;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvD,OAAA,CAACvB,GAAG;gBAAC+D,EAAE,EAAE;kBAAE+B,OAAO,EAAE,MAAM;kBAAEiB,QAAQ,EAAE,MAAM;kBAAEC,GAAG,EAAE,CAAC;kBAAEb,UAAU,EAAE,QAAQ;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAA3C,QAAA,gBAClF1C,OAAA,CAAChB,IAAI;kBACH0G,KAAK,EAAE,eAAe,CAACX,MAAM,CAAC/D,UAAU,GAAG,GAAG,EAAE2E,OAAO,CAAC,CAAC,CAAC,GAAI;kBAC9DjC,KAAK,EAAE9B,kBAAkB,CAACmD,MAAM,CAAC/D,UAAU,CAAE;kBAC7C2D,IAAI,EAAC;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACFvD,OAAA,CAACtB,UAAU;kBAACwE,OAAO,EAAC,SAAS;kBAACQ,KAAK,EAAC,eAAe;kBAAAhB,QAAA,EAChDqC,MAAM,CAAChE;gBAAS;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EAGLwB,MAAM,CAAC9D,OAAO,CAACqB,MAAM,GAAG,CAAC,iBACxBtC,OAAA,CAACvB,GAAG;gBAAC+D,EAAE,EAAE;kBAAE6C,EAAE,EAAE;gBAAE,CAAE;gBAAA3C,QAAA,gBACjB1C,OAAA,CAACtB,UAAU;kBAACwE,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAAAT,QAAA,EAAC;gBAE7C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACvB,GAAG;kBAAC+D,EAAE,EAAE;oBAAE+B,OAAO,EAAE,MAAM;oBAAEiB,QAAQ,EAAE,MAAM;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAA/C,QAAA,EACpDqC,MAAM,CAAC9D,OAAO,CAAC6D,GAAG,CAAC,CAACc,MAAM,EAAEC,GAAG,kBAC9B7F,OAAA,CAAChB,IAAI;oBAEH0G,KAAK,EAAEE,MAAO;oBACd1C,OAAO,EAAC,UAAU;oBAClByB,IAAI,EAAC;kBAAO,GAHPkB,GAAG;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIT,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA3EFwB,MAAM,CAACnE,EAAE;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4EJ,CACb,CAAC,EAED7C,OAAO,CAAC4B,MAAM,KAAK,CAAC,iBACnBtC,OAAA,CAACnB,KAAK;UAAC2D,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEqD,SAAS,EAAE;UAAS,CAAE;UAAApD,QAAA,eACvC1C,OAAA,CAACtB,UAAU;YAACwE,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,eAAe;YAAAhB,QAAA,EAAC;UAElD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACrD,EAAA,CAtOID,KAAe;AAAA8F,EAAA,GAAf9F,KAAe;AAwOrB,eAAeA,KAAK;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}