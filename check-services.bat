@echo off
echo ========================================
echo   LegalMind Pro - Service Status Check
echo ========================================
echo.

echo Checking Frontend (port 3000)...
curl -s http://localhost:3000 > nul
if %errorlevel% equ 0 (
    echo ✅ Frontend: RUNNING
) else (
    echo ❌ Frontend: NOT RUNNING
)

echo.
echo Checking Backend (port 5001)...
curl -s http://localhost:5001/health > nul
if %errorlevel% equ 0 (
    echo ✅ Backend: RUNNING
) else (
    echo ❌ Backend: NOT RUNNING
)

echo.
echo Checking AI Service (port 8000)...
curl -s http://localhost:8000/health > nul
if %errorlevel% equ 0 (
    echo ✅ AI Service: RUNNING
) else (
    echo ❌ AI Service: NOT RUNNING
)

echo.
echo Testing Authentication...
curl -s -X POST http://localhost:3000/api/auth/register -H "Content-Type: application/json" -d "{\"email\":\"<EMAIL>\",\"password\":\"password123\",\"name\":\"Test User\"}" > temp_auth_test.json
findstr "token" temp_auth_test.json > nul
if %errorlevel% equ 0 (
    echo ✅ Authentication: WORKING
) else (
    echo ❌ Authentication: FAILED
)
del temp_auth_test.json 2>nul

echo.
echo ========================================
echo   Service Status Check Complete
echo ========================================
echo.
echo Services should be available at:
echo   Frontend:   http://localhost:3000
echo   Backend:    http://localhost:5001
echo   AI Service: http://localhost:8000
echo.
pause
