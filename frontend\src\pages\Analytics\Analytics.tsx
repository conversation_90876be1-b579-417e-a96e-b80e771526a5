import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  LinearProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Description,
  Search,
  Star,
  Storage,
  Refresh,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface AnalyticsData {
  overview: {
    totalDocuments: number;
    totalQueries: number;
    totalStorageUsed: number;
    averageRating: number;
  };
  trends: {
    documents: number[];
    queries: number[];
    ratings: number[];
  };
  categories: {
    documents: Record<string, number>;
    queries: Record<string, number>;
  };
  performance: {
    averageProcessingTime: number;
    successRate: number;
    userSatisfaction: number;
  };
}

const Analytics: React.FC = () => {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock data for now - replace with actual API call
      const mockData: AnalyticsData = {
        overview: {
          totalDocuments: 24,
          totalQueries: 156,
          totalStorageUsed: 245 * 1024 * 1024, // 245 MB
          averageRating: 4.2,
        },
        trends: {
          documents: [5, 8, 12, 15, 18, 22, 24],
          queries: [20, 35, 45, 78, 95, 125, 156],
          ratings: [3.8, 3.9, 4.0, 4.1, 4.1, 4.2, 4.2],
        },
        categories: {
          documents: {
            'Dân sự': 8,
            'Hình sự': 3,
            'Hành chính': 5,
            'Thương mại': 6,
            'Lao động': 2,
          },
          queries: {
            'Tư vấn pháp lý': 89,
            'Tìm kiếm tài liệu': 34,
            'Phân tích vụ việc': 21,
            'Tra cứu quy định': 12,
          },
        },
        performance: {
          averageProcessingTime: 2.3,
          successRate: 0.96,
          userSatisfaction: 4.2,
        },
      };

      setTimeout(() => {
        setData(mockData);
        setLoading(false);
      }, 1000);
    } catch (err) {
      setError('Không thể tải dữ liệu thống kê');
      setLoading(false);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };



  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" action={
          <IconButton onClick={fetchAnalytics}>
            <Refresh />
          </IconButton>
        }>
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              Thống kê & Phân tích
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Tổng quan về hoạt động và hiệu suất của hệ thống
            </Typography>
          </Box>
          <Tooltip title="Làm mới dữ liệu">
            <IconButton onClick={fetchAnalytics}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Overview Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <Description />
                    </Avatar>
                    <Typography variant="h6">Tài liệu</Typography>
                  </Box>
                  <Typography variant="h4" component="div">
                    {data?.overview.totalDocuments}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Tổng số tài liệu đã tải lên
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <Search />
                    </Avatar>
                    <Typography variant="h6">Truy vấn</Typography>
                  </Box>
                  <Typography variant="h4" component="div">
                    {data?.overview.totalQueries}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Tổng số câu hỏi đã được xử lý
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                      <Storage />
                    </Avatar>
                    <Typography variant="h6">Dung lượng</Typography>
                  </Box>
                  <Typography variant="h4" component="div">
                    {formatBytes(data?.overview.totalStorageUsed || 0)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Dung lượng đã sử dụng
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                      <Star />
                    </Avatar>
                    <Typography variant="h6">Đánh giá</Typography>
                  </Box>
                  <Typography variant="h4" component="div">
                    {data?.overview.averageRating}/5
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Đánh giá trung bình
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>

        {/* Data Tables */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Xu hướng theo thời gian (7 tuần gần nhất)
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Tài liệu tải lên:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                    {data?.trends.documents.map((count, index) => (
                      <Chip
                        key={index}
                        label={`T${index + 1}: ${count}`}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                    ))}
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>
                    Truy vấn thực hiện:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {data?.trends.queries.map((count, index) => (
                      <Chip
                        key={index}
                        label={`T${index + 1}: ${count}`}
                        color="secondary"
                        variant="outlined"
                        size="small"
                      />
                    ))}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Phân loại tài liệu
                </Typography>
                <Box sx={{ mt: 2 }}>
                  {Object.entries(data?.categories.documents || {}).map(([category, count]) => (
                    <Box key={category} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">{category}</Typography>
                        <Typography variant="body2" fontWeight="bold">{count}</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={(count / Math.max(...Object.values(data?.categories.documents || {}))) * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Phân loại truy vấn
                </Typography>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  {Object.entries(data?.categories.queries || {}).map(([category, count]) => (
                    <Grid item xs={12} sm={6} md={3} key={category}>
                      <Box sx={{ textAlign: 'center', p: 2, border: 1, borderColor: 'divider', borderRadius: 2 }}>
                        <Typography variant="h4" color="primary" gutterBottom>
                          {count}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {category}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </motion.div>
    </Box>
  );
};

export default Analytics;
