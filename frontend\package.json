{"name": "legalmind-pro-frontend", "version": "1.0.0", "description": "LegalMind Pro Frontend - React + Material-UI", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.1", "@mui/lab": "^5.0.0-alpha.158", "@mui/material": "^5.15.1", "@mui/x-data-grid": "^6.18.3", "@mui/x-date-pickers": "^6.18.3", "@reduxjs/toolkit": "^2.0.1", "@types/chart.js": "^2.9.41", "axios": "^1.6.2", "chart.js": "^4.5.0", "dayjs": "^1.11.10", "framer-motion": "^10.16.16", "i18next": "^23.7.8", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "react-query": "^3.39.3", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.8.0", "web-vitals": "^3.5.0", "workbox-background-sync": "^7.0.0", "workbox-broadcast-update": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-google-analytics": "^7.0.0", "workbox-navigation-preload": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-range-requests": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "workbox-streams": "^7.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "serve": "serve -s build -l 3000"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "serve": "^14.2.1", "typescript": "^4.9.5"}, "proxy": "http://localhost:5001"}