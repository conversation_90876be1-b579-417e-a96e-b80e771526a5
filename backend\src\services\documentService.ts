import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import { logger, logDatabaseOperation, logPerformance } from '../utils/logger';
import {
  incrementDocumentUpload,
  observeDocumentProcessingTime,
  ACTIVE_DOCUMENT_PROCESSING
} from '../utils/metrics';
import { db, queryHelper } from '../config/database';
import { EmbeddingService } from './embeddingService';

export interface DocumentMetadata {
  userId: string;
  title: string;
  description?: string;
  category: 'civil' | 'criminal' | 'administrative' | 'commercial' | 'labor' | 'other';
  tags: string[];
  originalName: string;
  fileName: string;
  filePath: string;
  mimeType: string;
  size: number;
  processedContent?: any;
  extractedText?: string;
  chunks?: any[];
  embeddings?: any[];
  metadata?: {
    processingTime?: number;
    language?: string;
    confidence?: number;
    pageCount?: number;
  };
}

export interface DocumentQueryOptions {
  userId: string;
  page: number;
  limit: number;
  category?: string;
  search?: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export interface DocumentQueryResult {
  documents: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export class DocumentService {
  private embeddingService: EmbeddingService;

  constructor() {
    this.embeddingService = new EmbeddingService();
    logger.info('DocumentService initialized');
  }

  /**
   * Create a new document record
   */
  async createDocument(documentData: DocumentMetadata): Promise<any> {
    const startTime = Date.now();

    try {
      ACTIVE_DOCUMENT_PROCESSING.inc();

      // Prepare document data for database
      const documentRecord = {
        user_id: documentData.userId,
        title: documentData.title,
        description: documentData.description || null,
        category: documentData.category,
        tags: documentData.tags,
        original_name: documentData.originalName,
        file_name: documentData.fileName,
        file_path: documentData.filePath,
        mime_type: documentData.mimeType,
        file_size: documentData.size,
        status: 'processing',
        extracted_text: documentData.extractedText || null,
        processed_content: documentData.processedContent ? JSON.stringify(documentData.processedContent) : null,
        chunks: documentData.chunks ? JSON.stringify(documentData.chunks) : null,
        metadata: documentData.metadata ? JSON.stringify(documentData.metadata) : '{}',
        language: documentData.metadata?.language || 'vi',
        confidence_score: documentData.metadata?.confidence || null,
        processing_time: documentData.metadata?.processingTime || null,
      };

      // Insert into database
      const document = await queryHelper.insert('documents', documentRecord);

      // Store embeddings if available
      if (documentData.chunks && documentData.embeddings) {
        try {
          await this.embeddingService.storeDocumentEmbeddings(
            document.id,
            documentData.chunks,
            documentData.embeddings
          );

          // Update document status to processed
          await queryHelper.update('documents', document.id, { status: 'processed' });
        } catch (embeddingError) {
          logger.warn('Failed to store embeddings, document still created', {
            documentId: document.id,
            error: embeddingError.message,
          });
        }
      }

      const processingTime = Date.now() - startTime;

      // Record metrics
      incrementDocumentUpload(
        this.getFileTypeFromMimeType(documentData.mimeType),
        true
      );

      observeDocumentProcessingTime(
        this.getFileTypeFromMimeType(documentData.mimeType),
        this.getSizeCategory(documentData.size),
        processingTime / 1000
      );

      logDatabaseOperation('INSERT', 'documents', processingTime);
      logPerformance('createDocument', processingTime, { documentId: document.id });

      logger.info('Document created successfully', {
        documentId: document.id,
        userId: documentData.userId,
        fileName: documentData.originalName,
        size: documentData.size,
      });

      return document;

    } catch (error) {
      incrementDocumentUpload(
        this.getFileTypeFromMimeType(documentData.mimeType),
        false
      );
      
      logDatabaseOperation('INSERT', 'documents', Date.now() - startTime, error as Error);
      logger.error('Failed to create document', { error: error.message, documentData });
      throw error;
    } finally {
      ACTIVE_DOCUMENT_PROCESSING.dec();
    }
  }

  /**
   * Get user's documents with pagination and filtering
   */
  async getUserDocuments(options: DocumentQueryOptions): Promise<DocumentQueryResult> {
    const startTime = Date.now();

    try {
      const { userId, page, limit, category, search, sortBy, sortOrder } = options;
      const offset = (page - 1) * limit;

      // Build query conditions
      let whereConditions = ['user_id = $1', 'deleted_at IS NULL'];
      let queryParams: any[] = [userId];
      let paramIndex = 2;

      // Apply category filter
      if (category) {
        whereConditions.push(`category = $${paramIndex}`);
        queryParams.push(category);
        paramIndex++;
      }

      // Apply search filter
      if (search) {
        whereConditions.push(`(title ILIKE $${paramIndex} OR extracted_text ILIKE $${paramIndex})`);
        queryParams.push(`%${search}%`);
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');

      // Build sort clause
      const validSortFields = ['created_at', 'title', 'file_size', 'updated_at'];
      const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
      const sortDirection = sortOrder === 'asc' ? 'ASC' : 'DESC';

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM documents WHERE ${whereClause}`;
      const countResult = await db.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].count, 10);

      // Get documents
      const documentsQuery = `
        SELECT * FROM documents
        WHERE ${whereClause}
        ORDER BY ${sortField} ${sortDirection}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      queryParams.push(limit, offset);

      const documentsResult = await db.query(documentsQuery, queryParams);
      const documents = documentsResult.rows.map(this.transformDatabaseDocument);

      // Calculate pagination
      const totalPages = Math.ceil(total / limit);

      const result: DocumentQueryResult = {
        documents,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };

      const processingTime = Date.now() - startTime;
      logDatabaseOperation('SELECT', 'documents', processingTime);
      logPerformance('getUserDocuments', processingTime, { userId, resultCount: documents.length });

      return result;

    } catch (error) {
      logDatabaseOperation('SELECT', 'documents', Date.now() - startTime, error as Error);
      logger.error('Failed to get user documents', { error: error.message, options });
      throw error;
    }
  }

  /**
   * Get document by ID
   */
  async getDocumentById(documentId: string, userId: string): Promise<any | null> {
    const startTime = Date.now();

    try {
      const result = await db.query(
        'SELECT * FROM documents WHERE id = $1 AND user_id = $2 AND deleted_at IS NULL',
        [documentId, userId]
      );

      const document = result.rows[0];
      if (!document) {
        return null;
      }

      const transformedDocument = this.transformDatabaseDocument(document);

      const processingTime = Date.now() - startTime;
      logDatabaseOperation('SELECT', 'documents', processingTime);
      logPerformance('getDocumentById', processingTime, { documentId, userId });

      return transformedDocument;

    } catch (error) {
      logDatabaseOperation('SELECT', 'documents', Date.now() - startTime, error as Error);
      logger.error('Failed to get document by ID', { error: error.message, documentId, userId });
      throw error;
    }
  }

  /**
   * Delete document
   */
  async deleteDocument(documentId: string, userId: string): Promise<boolean> {
    const startTime = Date.now();

    try {
      // First check if document exists and belongs to user
      const document = await this.getDocumentById(documentId, userId);
      
      if (!document) {
        return false;
      }

      // Delete file from filesystem
      try {
        await fs.unlink(document.filePath);
        logger.info('Document file deleted', { filePath: document.filePath });
      } catch (fileError) {
        logger.warn('Failed to delete document file', {
          filePath: document.filePath,
          error: fileError.message
        });
      }

      // Delete embeddings first
      try {
        await this.embeddingService.deleteDocumentEmbeddings(documentId);
      } catch (embeddingError) {
        logger.warn('Failed to delete embeddings', {
          documentId,
          error: embeddingError.message,
        });
      }

      // Soft delete from database
      const result = await queryHelper.softDelete('documents', documentId);

      const processingTime = Date.now() - startTime;
      logDatabaseOperation('DELETE', 'documents', processingTime);
      logPerformance('deleteDocument', processingTime, { documentId, userId });

      logger.info('Document deleted successfully', { documentId, userId });
      return !!result;

    } catch (error) {
      logDatabaseOperation('DELETE', 'documents', Date.now() - startTime, error as Error);
      logger.error('Failed to delete document', { error: error.message, documentId, userId });
      throw error;
    }
  }

  /**
   * Reprocess document with updated AI models
   */
  async reprocessDocument(documentId: string, userId: string): Promise<any | null> {
    const startTime = Date.now();

    try {
      // Get document from database
      const document = await this.getDocumentById(documentId, userId);

      if (!document) {
        return null;
      }

      ACTIVE_DOCUMENT_PROCESSING.inc();

      // Update document status and metadata
      const updateData = {
        status: 'processing',
        metadata: JSON.stringify({
          ...document.metadata,
          reprocessedAt: new Date().toISOString(),
        }),
      };

      const updatedDocument = await queryHelper.update('documents', documentId, updateData);

      const processingTime = Date.now() - startTime;

      observeDocumentProcessingTime(
        this.getFileTypeFromMimeType(document.mimeType),
        this.getSizeCategory(document.size),
        processingTime / 1000
      );

      logDatabaseOperation('UPDATE', 'documents', processingTime);
      logPerformance('reprocessDocument', processingTime, { documentId, userId });

      logger.info('Document reprocessed successfully', { documentId, userId });

      return {
        document: this.transformDatabaseDocument(updatedDocument),
        processingTime,
      };

    } catch (error) {
      logDatabaseOperation('UPDATE', 'documents', Date.now() - startTime, error as Error);
      logger.error('Failed to reprocess document', { error: error.message, documentId, userId });
      throw error;
    } finally {
      ACTIVE_DOCUMENT_PROCESSING.dec();
    }
  }

  /**
   * Get document statistics for user
   */
  async getUserDocumentStats(userId: string): Promise<any> {
    const startTime = Date.now();
    
    try {
      const userDocuments = Array.from(this.documents.values())
        .filter(doc => doc.userId === userId);

      const stats = {
        total: userDocuments.length,
        byCategory: {} as Record<string, number>,
        byStatus: {} as Record<string, number>,
        totalSize: 0,
        recentUploads: 0, // Last 30 days
      };

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      userDocuments.forEach(doc => {
        // Count by category
        stats.byCategory[doc.category] = (stats.byCategory[doc.category] || 0) + 1;
        
        // Count by status
        stats.byStatus[doc.status] = (stats.byStatus[doc.status] || 0) + 1;
        
        // Sum total size
        stats.totalSize += doc.size;
        
        // Count recent uploads
        if (new Date(doc.createdAt) > thirtyDaysAgo) {
          stats.recentUploads++;
        }
      });

      const processingTime = Date.now() - startTime;
      logPerformance('getUserDocumentStats', processingTime, { userId, documentCount: userDocuments.length });

      return stats;

    } catch (error) {
      logger.error('Failed to get user document stats', { error: error.message, userId });
      throw error;
    }
  }

  /**
   * Helper methods
   */
  private getFileTypeFromMimeType(mimeType: string): string {
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'doc';
    if (mimeType.includes('text')) return 'txt';
    return 'other';
  }

  private getSizeCategory(size: number): string {
    if (size < 1024 * 1024) return 'small'; // < 1MB
    if (size < 10 * 1024 * 1024) return 'medium'; // < 10MB
    return 'large'; // >= 10MB
  }

  /**
   * Transform database document to API format
   */
  private transformDatabaseDocument(dbDoc: any): any {
    return {
      id: dbDoc.id,
      title: dbDoc.title,
      description: dbDoc.description,
      category: dbDoc.category,
      tags: dbDoc.tags || [],
      originalName: dbDoc.original_name,
      fileName: dbDoc.file_name,
      filePath: dbDoc.file_path,
      mimeType: dbDoc.mime_type,
      size: dbDoc.file_size,
      status: dbDoc.status,
      extractedText: dbDoc.extracted_text,
      processedContent: dbDoc.processed_content ? JSON.parse(dbDoc.processed_content) : null,
      chunks: dbDoc.chunks ? JSON.parse(dbDoc.chunks) : null,
      metadata: dbDoc.metadata ? JSON.parse(dbDoc.metadata) : {},
      language: dbDoc.language,
      confidenceScore: dbDoc.confidence_score,
      processingTime: dbDoc.processing_time,
      createdAt: dbDoc.created_at,
      updatedAt: dbDoc.updated_at,
      userId: dbDoc.user_id,
    };
  }

  /**
   * Search documents by content
   */
  async searchDocuments(userId: string, query: string, options?: {
    category?: string;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    const startTime = Date.now();

    try {
      const { category, limit = 20, offset = 0 } = options || {};

      // Build query conditions
      let whereConditions = ['user_id = $1', 'deleted_at IS NULL'];
      let queryParams: any[] = [userId];
      let paramIndex = 2;

      if (category) {
        whereConditions.push(`category = $${paramIndex}`);
        queryParams.push(category);
        paramIndex++;
      }

      // Add text search condition
      whereConditions.push(`(title ILIKE $${paramIndex} OR extracted_text ILIKE $${paramIndex})`);
      queryParams.push(`%${query}%`);
      paramIndex++;

      const whereClause = whereConditions.join(' AND ');

      // Execute search query
      const searchQuery = `
        SELECT * FROM documents
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      queryParams.push(limit, offset);

      const result = await db.query(searchQuery, queryParams);
      const searchResults = result.rows.map(this.transformDatabaseDocument);

      const processingTime = Date.now() - startTime;
      logPerformance('searchDocuments', processingTime, {
        userId,
        query,
        resultCount: searchResults.length
      });

      return searchResults;

    } catch (error) {
      logger.error('Failed to search documents', { error: error.message, userId, query });
      throw error;
    }
  }
}
