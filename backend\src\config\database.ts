import { Pool, PoolClient } from 'pg';
import { config } from './config';
import { logger } from '../utils/logger';
import { DATABASE_CONNECTIONS, DATABASE_QUERY_DURATION } from '../utils/metrics';

export class DatabaseConnection {
  private pool: Pool;
  private static instance: DatabaseConnection;

  private constructor() {
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.name,
      user: config.database.username,
      password: config.database.password,
      ssl: config.database.ssl,
      max: parseInt(process.env.DB_POOL_MAX || '10', 10),
      min: parseInt(process.env.DB_POOL_MIN || '2', 10),
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    // Pool event handlers
    this.pool.on('connect', (client) => {
      logger.debug('New database client connected');
      DATABASE_CONNECTIONS.labels('main').inc();
    });

    this.pool.on('remove', (client) => {
      logger.debug('Database client removed');
      DATABASE_CONNECTIONS.labels('main').dec();
    });

    this.pool.on('error', (err, client) => {
      logger.error('Database pool error', { error: err.message });
    });

    logger.info('Database connection pool initialized', {
      host: config.database.host,
      port: config.database.port,
      database: config.database.name,
      maxConnections: parseInt(process.env.DB_POOL_MAX || '10', 10),
    });
  }

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public async query(text: string, params?: any[]): Promise<any> {
    const start = Date.now();
    const client = await this.pool.connect();
    
    try {
      const result = await client.query(text, params);
      const duration = Date.now() - start;
      
      DATABASE_QUERY_DURATION.labels('query', 'unknown').observe(duration / 1000);
      
      logger.debug('Database query executed', {
        query: text.substring(0, 100),
        duration: `${duration}ms`,
        rowCount: result.rowCount,
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      DATABASE_QUERY_DURATION.labels('query_error', 'unknown').observe(duration / 1000);
      
      logger.error('Database query failed', {
        query: text.substring(0, 100),
        error: error.message,
        duration: `${duration}ms`,
      });
      
      throw error;
    } finally {
      client.release();
    }
  }

  public async getClient(): Promise<PoolClient> {
    return this.pool.connect();
  }

  public async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  public async close(): Promise<void> {
    await this.pool.end();
    logger.info('Database connection pool closed');
  }

  public getPoolInfo() {
    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
    };
  }
}

// Export singleton instance
export const db = DatabaseConnection.getInstance();

// Helper function for common query patterns
export const queryHelper = {
  async findById(table: string, id: string): Promise<any> {
    const result = await db.query(`SELECT * FROM ${table} WHERE id = $1`, [id]);
    return result.rows[0] || null;
  },

  async findByUserId(table: string, userId: string): Promise<any[]> {
    const result = await db.query(`SELECT * FROM ${table} WHERE user_id = $1 ORDER BY created_at DESC`, [userId]);
    return result.rows;
  },

  async insert(table: string, data: Record<string, any>): Promise<any> {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const placeholders = keys.map((_, index) => `$${index + 1}`).join(', ');
    const columns = keys.join(', ');

    const query = `INSERT INTO ${table} (${columns}) VALUES (${placeholders}) RETURNING *`;
    const result = await db.query(query, values);
    return result.rows[0];
  },

  async update(table: string, id: string, data: Record<string, any>): Promise<any> {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const setClause = keys.map((key, index) => `${key} = $${index + 2}`).join(', ');

    const query = `UPDATE ${table} SET ${setClause}, updated_at = NOW() WHERE id = $1 RETURNING *`;
    const result = await db.query(query, [id, ...values]);
    return result.rows[0];
  },

  async delete(table: string, id: string): Promise<boolean> {
    const result = await db.query(`DELETE FROM ${table} WHERE id = $1`, [id]);
    return result.rowCount > 0;
  },

  async softDelete(table: string, id: string): Promise<any> {
    const result = await db.query(
      `UPDATE ${table} SET deleted_at = NOW(), updated_at = NOW() WHERE id = $1 AND deleted_at IS NULL RETURNING *`,
      [id]
    );
    return result.rows[0];
  },
};
