import axios from 'axios';
import { logger, logAIOperation, logPerformance } from '../utils/logger';
import {
  incrementAIModelRequest,
  observeAIModelLatency,
  observeQueryProcessingTime,
  QUERY_TOKENS_USED
} from '../utils/metrics';
import { DocumentProcessor } from './documentProcessor';

export interface ProcessDocumentRequest {
  filePath: string;
  originalName: string;
  mimeType: string;
  size: number;
}

export interface ProcessDocumentResponse {
  content: any;
  text: string;
  chunks: any[];
  embeddings: any[];
  language: string;
  confidence: number;
  pageCount?: number;
}

export interface LegalQueryRequest {
  query: string;
  context?: any;
  userId: string;
  queryType?: 'legal_advice' | 'document_search' | 'case_analysis' | 'regulation_lookup';
}

export interface LegalQueryResponse {
  response: string;
  sources: any[];
  confidence: number;
  tokensUsed: number;
  processingTime: number;
  metadata: any;
}

export interface EmbeddingRequest {
  text: string;
  model?: string;
}

export interface EmbeddingResponse {
  embedding: number[];
  model: string;
  tokensUsed: number;
}

export class AIService {
  private aiServiceUrl: string;
  private timeout: number;
  private documentProcessor: DocumentProcessor;

  constructor() {
    this.aiServiceUrl = process.env.AI_SERVICE_URL || 'http://localhost:8000';
    this.timeout = parseInt(process.env.AI_SERVICE_TIMEOUT || '30000');
    this.documentProcessor = new DocumentProcessor();

    logger.info('AIService initialized', {
      aiServiceUrl: this.aiServiceUrl,
      timeout: this.timeout
    });
  }

  /**
   * Process document with AI service
   */
  async processDocument(request: ProcessDocumentRequest): Promise<ProcessDocumentResponse> {
    const startTime = Date.now();
    const operation = 'process_document';
    const model = 'vietnamese-legal-processor';

    try {
      logger.info('Processing document with AI service', {
        fileName: request.originalName,
        mimeType: request.mimeType,
        size: request.size,
      });

      // Process document with real document processor
      const response = await this.documentProcessor.processDocument(request);

      const processingTime = Date.now() - startTime;
      
      // Record metrics
      incrementAIModelRequest(model, operation, true);
      observeAIModelLatency(model, operation, processingTime / 1000);

      logAIOperation(operation, model, undefined, processingTime);
      logPerformance('processDocument', processingTime, {
        fileName: request.originalName,
        size: request.size,
      });

      logger.info('Document processed successfully', {
        fileName: request.originalName,
        processingTime,
        confidence: response.confidence,
        chunkCount: response.chunks.length,
      });

      return response;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      incrementAIModelRequest(model, operation, false);
      logAIOperation(operation, model, undefined, processingTime, error as Error);

      logger.error('Failed to process document', {
        error: error.message,
        fileName: request.originalName,
        processingTime,
      });

      throw new Error(`Document processing failed: ${error.message}`);
    }
  }

  /**
   * Process legal query with AI service
   */
  async processLegalQuery(request: LegalQueryRequest): Promise<LegalQueryResponse> {
    const startTime = Date.now();
    const operation = 'legal_query';
    const model = 'vietnamese-legal-rag';

    try {
      logger.info('Processing legal query', {
        userId: request.userId,
        queryType: request.queryType,
        queryLength: request.query.length,
      });

      const response = await axios.post(
        `${this.aiServiceUrl}/api/v1/query/legal`,
        {
          query: request.query,
          context: request.context,
          query_type: request.queryType || 'legal_advice',
        },
        {
          timeout: this.timeout,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'LegalMind-Backend/1.0.0',
          },
        }
      );

      const processingTime = Date.now() - startTime;
      const result: LegalQueryResponse = {
        response: response.data.response || 'Không thể xử lý câu hỏi này.',
        sources: response.data.sources || [],
        confidence: response.data.confidence || 0.5,
        tokensUsed: response.data.tokens_used || 0,
        processingTime,
        metadata: response.data.metadata || {},
      };

      // Record metrics
      incrementAIModelRequest(model, operation, true);
      observeAIModelLatency(model, operation, processingTime / 1000);
      observeQueryProcessingTime(request.queryType || 'legal_advice', processingTime / 1000);
      QUERY_TOKENS_USED.labels(model).observe(result.tokensUsed);

      logAIOperation(operation, model, result.tokensUsed, processingTime);
      logPerformance('processLegalQuery', processingTime, {
        userId: request.userId,
        queryType: request.queryType,
        tokensUsed: result.tokensUsed,
        confidence: result.confidence,
      });

      logger.info('Legal query processed successfully', {
        userId: request.userId,
        processingTime,
        tokensUsed: result.tokensUsed,
        confidence: result.confidence,
        sourceCount: result.sources.length,
      });

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      incrementAIModelRequest(model, operation, false);
      logAIOperation(operation, model, undefined, processingTime, error as Error);

      logger.error('Failed to process legal query', {
        error: error.message,
        userId: request.userId,
        queryType: request.queryType,
        processingTime,
      });

      // Return a fallback response instead of throwing
      return {
        response: 'Xin lỗi, hiện tại hệ thống AI đang gặp sự cố. Vui lòng thử lại sau.',
        sources: [],
        confidence: 0,
        tokensUsed: 0,
        processingTime,
        metadata: { error: true, message: error.message },
      };
    }
  }

  /**
   * Create embeddings for text
   */
  async createEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const startTime = Date.now();
    const operation = 'create_embedding';
    const model = request.model || 'vietnamese-embedder';

    try {
      logger.debug('Creating embedding', {
        textLength: request.text.length,
        model,
      });

      const response = await axios.post(
        `${this.aiServiceUrl}/api/v1/embeddings/create`,
        {
          text: request.text,
          model: model,
        },
        {
          timeout: this.timeout,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const processingTime = Date.now() - startTime;
      const result: EmbeddingResponse = {
        embedding: response.data.embedding || [],
        model: response.data.model || model,
        tokensUsed: response.data.tokens_used || 0,
      };

      // Record metrics
      incrementAIModelRequest(model, operation, true);
      observeAIModelLatency(model, operation, processingTime / 1000);

      logAIOperation(operation, model, result.tokensUsed, processingTime);

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      incrementAIModelRequest(model, operation, false);
      logAIOperation(operation, model, undefined, processingTime, error as Error);

      logger.error('Failed to create embedding', {
        error: error.message,
        textLength: request.text.length,
        model,
        processingTime,
      });

      throw new Error(`Embedding creation failed: ${error.message}`);
    }
  }

  /**
   * Chunk text for processing
   */
  async chunkText(text: string, options?: {
    chunkSize?: number;
    overlap?: number;
    preserveStructure?: boolean;
  }): Promise<any[]> {
    const startTime = Date.now();
    const operation = 'chunk_text';
    const model = 'vietnamese-chunker';

    try {
      logger.debug('Chunking text', {
        textLength: text.length,
        options,
      });

      const response = await axios.post(
        `${this.aiServiceUrl}/api/v1/text/chunk`,
        {
          text,
          chunk_size: options?.chunkSize || 1000,
          overlap: options?.overlap || 100,
          preserve_structure: options?.preserveStructure || true,
        },
        {
          timeout: this.timeout,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const processingTime = Date.now() - startTime;
      const chunks = response.data.chunks || [];

      // Record metrics
      incrementAIModelRequest(model, operation, true);
      observeAIModelLatency(model, operation, processingTime / 1000);

      logAIOperation(operation, model, undefined, processingTime);
      logPerformance('chunkText', processingTime, {
        textLength: text.length,
        chunkCount: chunks.length,
      });

      return chunks;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      incrementAIModelRequest(model, operation, false);
      logAIOperation(operation, model, undefined, processingTime, error as Error);

      logger.error('Failed to chunk text', {
        error: error.message,
        textLength: text.length,
        processingTime,
      });

      throw new Error(`Text chunking failed: ${error.message}`);
    }
  }

  /**
   * Health check for AI service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.aiServiceUrl}/health`, {
        timeout: 5000,
      });
      
      return response.status === 200 && response.data.status === 'healthy';
    } catch (error) {
      logger.warn('AI service health check failed', { error: error.message });
      return false;
    }
  }

  /**
   * Get AI service status and metrics
   */
  async getServiceStatus(): Promise<any> {
    try {
      const response = await axios.get(`${this.aiServiceUrl}/`, {
        timeout: 5000,
      });
      
      return response.data;
    } catch (error) {
      logger.error('Failed to get AI service status', { error: error.message });
      throw error;
    }
  }


}
