{"name": "@types/chart.js", "version": "2.9.41", "description": "TypeScript definitions for chart.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chart.js", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "anuti", "url": "https://github.com/anuti"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "FabienLavocat", "url": "https://github.com/FabienLavocat"}, {"name": "KentarouTakeda", "githubUsername": "KentarouTakeda", "url": "https://github.com/KentarouTakeda"}, {"name": "<PERSON>", "githubUsername": "larry<PERSON>r", "url": "https://github.com/larrybahr"}, {"name": "<PERSON>", "githubUsername": "mernen", "url": "https://github.com/mernen"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/josefpaij"}, {"name": "<PERSON>", "githubUsername": "dan<PERSON>", "url": "https://github.com/danmana"}, {"name": "<PERSON>", "githubUsername": "guillaume-ro-fr", "url": "https://github.com/guillaume-ro-fr"}, {"name": "<PERSON>", "githubUsername": "archy-bold", "url": "https://github.com/archy-bold"}, {"name": "<PERSON>", "githubUsername": "braincore", "url": "https://github.com/braincore"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "alex<PERSON>", "url": "https://github.com/alexdor"}, {"name": "<PERSON>", "githubUsername": "ma<PERSON><PERSON>", "url": "https://github.com/mahnuh"}, {"name": "<PERSON>", "githubUsername": "Conrad777", "url": "https://github.com/Conrad777"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "adripanico", "url": "https://github.com/adripanico"}, {"name": "wert<PERSON>i", "githubUsername": "wert<PERSON>i", "url": "https://github.com/wertzui"}, {"name": "<PERSON>", "githubUsername": "lekoaf", "url": "https://github.com/lekoaf"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ElianCordoba"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "indigolain", "url": "https://github.com/indigolain"}, {"name": "<PERSON>", "githubUsername": "r<PERSON><PERSON><PERSON>", "url": "https://github.com/ricmello"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rnicholus"}, {"name": "<PERSON>", "githubUsername": "mrjack88", "url": "https://github.com/mrjack88"}, {"name": "<PERSON>", "githubUsername": "canoceto", "url": "https://github.com/canoceto"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "nobu222", "url": "https://github.com/nobu222"}, {"name": "<PERSON>", "githubUsername": "Marcoru97", "url": "https://github.com/Marcoru97"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tonybadguy"}, {"name": "<PERSON>", "githubUsername": "Ilmarinen100", "url": "https://github.com/Ilmarinen100"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "IVIosi", "url": "https://github.com/IVIosi"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/samar<PERSON>han"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/chart.js"}, "scripts": {}, "dependencies": {"moment": "^2.10.2"}, "typesPublisherContentHash": "2577c7a5b63f6e12990e9671791fb201d769f04ab51c79989089157278e95de9", "typeScriptVersion": "4.5"}