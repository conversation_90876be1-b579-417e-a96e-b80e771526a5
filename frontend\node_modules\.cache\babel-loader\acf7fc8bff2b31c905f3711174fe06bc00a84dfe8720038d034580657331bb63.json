{"ast": null, "code": "var _jsxFileName = \"E:\\\\Project\\\\AI\\\\RAG\\\\Law\\\\LegalMind Pro - AI Assistant Ph\\xE1p Lu\\u1EADt Th\\xF4ng Minh\\\\frontend\\\\src\\\\pages\\\\Analytics\\\\Analytics.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CircularProgress, Alert, Avatar, IconButton, Tooltip } from '@mui/material';\nimport { Description, Search, Star, Storage, Refresh } from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Analytics = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchAnalytics();\n  }, []);\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Mock data for now - replace with actual API call\n      const mockData = {\n        overview: {\n          totalDocuments: 24,\n          totalQueries: 156,\n          totalStorageUsed: 245 * 1024 * 1024,\n          // 245 MB\n          averageRating: 4.2\n        },\n        trends: {\n          documents: [5, 8, 12, 15, 18, 22, 24],\n          queries: [20, 35, 45, 78, 95, 125, 156],\n          ratings: [3.8, 3.9, 4.0, 4.1, 4.1, 4.2, 4.2]\n        },\n        categories: {\n          documents: {\n            'Dân sự': 8,\n            'Hình sự': 3,\n            'Hành chính': 5,\n            'Thương mại': 6,\n            'Lao động': 2\n          },\n          queries: {\n            'Tư vấn pháp lý': 89,\n            'Tìm kiếm tài liệu': 34,\n            'Phân tích vụ việc': 21,\n            'Tra cứu quy định': 12\n          }\n        },\n        performance: {\n          averageProcessingTime: 2.3,\n          successRate: 0.96,\n          userSatisfaction: 4.2\n        }\n      };\n      setTimeout(() => {\n        setData(mockData);\n        setLoading(false);\n      }, 1000);\n    } catch (err) {\n      setError('Không thể tải dữ liệu thống kê');\n      setLoading(false);\n    }\n  };\n  const formatBytes = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '60vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        action: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: fetchAnalytics,\n          children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 4,\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            gutterBottom: true,\n            children: \"Th\\u1ED1ng k\\xEA & Ph\\xE2n t\\xEDch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"textSecondary\",\n            children: \"T\\u1ED5ng quan v\\u1EC1 ho\\u1EA1t \\u0111\\u1ED9ng v\\xE0 hi\\u1EC7u su\\u1EA5t c\\u1EE7a h\\u1EC7 th\\u1ED1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"L\\xE0m m\\u1EDBi d\\u1EEF li\\u1EC7u\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: fetchAnalytics,\n            children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: 'primary.main',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Description, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"T\\xE0i li\\u1EC7u\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"div\",\n                  children: data === null || data === void 0 ? void 0 : data.overview.totalDocuments\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"T\\u1ED5ng s\\u1ED1 t\\xE0i li\\u1EC7u \\u0111\\xE3 t\\u1EA3i l\\xEAn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: 'secondary.main',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"Truy v\\u1EA5n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"div\",\n                  children: data === null || data === void 0 ? void 0 : data.overview.totalQueries\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"T\\u1ED5ng s\\u1ED1 c\\xE2u h\\u1ECFi \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c x\\u1EED l\\xFD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: 'success.main',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Storage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"Dung l\\u01B0\\u1EE3ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"div\",\n                  children: formatBytes((data === null || data === void 0 ? void 0 : data.overview.totalStorageUsed) || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Dung l\\u01B0\\u1EE3ng \\u0111\\xE3 s\\u1EED d\\u1EE5ng\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.4\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: 'warning.main',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"\\u0110\\xE1nh gi\\xE1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"div\",\n                  children: [data === null || data === void 0 ? void 0 : data.overview.averageRating, \"/5\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"\\u0110\\xE1nh gi\\xE1 trung b\\xECnh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Xu h\\u01B0\\u1EDBng theo th\\u1EDDi gian\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                data: lineChartData,\n                options: chartOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Ph\\xE2n lo\\u1EA1i t\\xE0i li\\u1EC7u\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Doughnut, {\n                data: doughnutData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"RiL7vLwmC7ZWXKL/bXt2EIBjBYk=\");\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Avatar", "IconButton", "<PERSON><PERSON><PERSON>", "Description", "Search", "Star", "Storage", "Refresh", "motion", "jsxDEV", "_jsxDEV", "Analytics", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "fetchAnalytics", "mockData", "overview", "totalDocuments", "totalQueries", "totalStorageUsed", "averageRating", "trends", "documents", "queries", "ratings", "categories", "performance", "averageProcessingTime", "successRate", "userSatisfaction", "setTimeout", "err", "formatBytes", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "sx", "display", "justifyContent", "alignItems", "height", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "severity", "action", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "duration", "mb", "variant", "gutterBottom", "color", "title", "container", "spacing", "item", "xs", "sm", "md", "delay", "bgcolor", "mr", "component", "Line", "lineChartData", "options", "chartOptions", "Doughnut", "doughnutData", "_c", "$RefreshReg$"], "sources": ["E:/Project/AI/RAG/Law/LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>/frontend/src/pages/Analytics/Analytics.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n  Chip,\n  Avatar,\n  LinearProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Analytics as AnalyticsIcon,\n  TrendingUp,\n  Description,\n  Search,\n  Star,\n  Storage,\n  Refresh,\n  Schedule,\n  CheckCircle,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\n\ninterface AnalyticsData {\n  overview: {\n    totalDocuments: number;\n    totalQueries: number;\n    totalStorageUsed: number;\n    averageRating: number;\n  };\n  trends: {\n    documents: number[];\n    queries: number[];\n    ratings: number[];\n  };\n  categories: {\n    documents: Record<string, number>;\n    queries: Record<string, number>;\n  };\n  performance: {\n    averageProcessingTime: number;\n    successRate: number;\n    userSatisfaction: number;\n  };\n}\n\nconst Analytics: React.FC = () => {\n  const [data, setData] = useState<AnalyticsData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchAnalytics();\n  }, []);\n\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // Mock data for now - replace with actual API call\n      const mockData: AnalyticsData = {\n        overview: {\n          totalDocuments: 24,\n          totalQueries: 156,\n          totalStorageUsed: 245 * 1024 * 1024, // 245 MB\n          averageRating: 4.2,\n        },\n        trends: {\n          documents: [5, 8, 12, 15, 18, 22, 24],\n          queries: [20, 35, 45, 78, 95, 125, 156],\n          ratings: [3.8, 3.9, 4.0, 4.1, 4.1, 4.2, 4.2],\n        },\n        categories: {\n          documents: {\n            'Dân sự': 8,\n            'Hình sự': 3,\n            'Hành chính': 5,\n            'Thương mại': 6,\n            'Lao động': 2,\n          },\n          queries: {\n            'Tư vấn pháp lý': 89,\n            'Tìm kiếm tài liệu': 34,\n            'Phân tích vụ việc': 21,\n            'Tra cứu quy định': 12,\n          },\n        },\n        performance: {\n          averageProcessingTime: 2.3,\n          successRate: 0.96,\n          userSatisfaction: 4.2,\n        },\n      };\n\n      setTimeout(() => {\n        setData(mockData);\n        setLoading(false);\n      }, 1000);\n    } catch (err) {\n      setError('Không thể tải dữ liệu thống kê');\n      setLoading(false);\n    }\n  };\n\n  const formatBytes = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"error\" action={\n          <IconButton onClick={fetchAnalytics}>\n            <Refresh />\n          </IconButton>\n        }>\n          {error}\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        {/* Header */}\n        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Box>\n            <Typography variant=\"h4\" gutterBottom>\n              Thống kê & Phân tích\n            </Typography>\n            <Typography variant=\"body1\" color=\"textSecondary\">\n              Tổng quan về hoạt động và hiệu suất của hệ thống\n            </Typography>\n          </Box>\n          <Tooltip title=\"Làm mới dữ liệu\">\n            <IconButton onClick={fetchAnalytics}>\n              <Refresh />\n            </IconButton>\n          </Tooltip>\n        </Box>\n\n        {/* Overview Cards */}\n        <Grid container spacing={3} sx={{ mb: 4 }}>\n          <Grid item xs={12} sm={6} md={3}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>\n                      <Description />\n                    </Avatar>\n                    <Typography variant=\"h6\">Tài liệu</Typography>\n                  </Box>\n                  <Typography variant=\"h4\" component=\"div\">\n                    {data?.overview.totalDocuments}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Tổng số tài liệu đã tải lên\n                  </Typography>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>\n                      <Search />\n                    </Avatar>\n                    <Typography variant=\"h6\">Truy vấn</Typography>\n                  </Box>\n                  <Typography variant=\"h4\" component=\"div\">\n                    {data?.overview.totalQueries}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Tổng số câu hỏi đã được xử lý\n                  </Typography>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n            >\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>\n                      <Storage />\n                    </Avatar>\n                    <Typography variant=\"h6\">Dung lượng</Typography>\n                  </Box>\n                  <Typography variant=\"h4\" component=\"div\">\n                    {formatBytes(data?.overview.totalStorageUsed || 0)}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Dung lượng đã sử dụng\n                  </Typography>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n            >\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>\n                      <Star />\n                    </Avatar>\n                    <Typography variant=\"h6\">Đánh giá</Typography>\n                  </Box>\n                  <Typography variant=\"h4\" component=\"div\">\n                    {data?.overview.averageRating}/5\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Đánh giá trung bình\n                  </Typography>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n        </Grid>\n\n        {/* Charts */}\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={8}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Xu hướng theo thời gian\n                </Typography>\n                <Line data={lineChartData} options={chartOptions} />\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} md={4}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Phân loại tài liệu\n                </Typography>\n                <Doughnut data={doughnutData} />\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </motion.div>\n    </Box>\n  );\n};\n\nexport default Analytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,gBAAgB,EAChBC,KAAK,EAELC,MAAM,EAGNC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SAGEC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,OAAO,QAGF,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAyBvC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAuB,IAAI,CAAC;EAC5D,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd2B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAME,QAAuB,GAAG;QAC9BC,QAAQ,EAAE;UACRC,cAAc,EAAE,EAAE;UAClBC,YAAY,EAAE,GAAG;UACjBC,gBAAgB,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;UAAE;UACrCC,aAAa,EAAE;QACjB,CAAC;QACDC,MAAM,EAAE;UACNC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UACrCC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UACvCC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QAC7C,CAAC;QACDC,UAAU,EAAE;UACVH,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,CAAC;YACZ,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YACf,UAAU,EAAE;UACd,CAAC;UACDC,OAAO,EAAE;YACP,gBAAgB,EAAE,EAAE;YACpB,mBAAmB,EAAE,EAAE;YACvB,mBAAmB,EAAE,EAAE;YACvB,kBAAkB,EAAE;UACtB;QACF,CAAC;QACDG,WAAW,EAAE;UACXC,qBAAqB,EAAE,GAAG;UAC1BC,WAAW,EAAE,IAAI;UACjBC,gBAAgB,EAAE;QACpB;MACF,CAAC;MAEDC,UAAU,CAAC,MAAM;QACfrB,OAAO,CAACM,QAAQ,CAAC;QACjBJ,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZlB,QAAQ,CAAC,gCAAgC,CAAC;MAC1CF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,WAAW,GAAIC,KAAa,IAAK;IACrC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAID,IAAI1B,OAAO,EAAE;IACX,oBACEL,OAAA,CAACjB,GAAG;MAACuD,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAC,QAAA,eAC3F3C,OAAA,CAACZ,gBAAgB;QAACwD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAIzC,KAAK,EAAE;IACT,oBACEP,OAAA,CAACjB,GAAG;MAACuD,EAAE,EAAE;QAAEW,CAAC,EAAE;MAAE,CAAE;MAAAN,QAAA,eAChB3C,OAAA,CAACX,KAAK;QAAC6D,QAAQ,EAAC,OAAO;QAACC,MAAM,eAC5BnD,OAAA,CAACT,UAAU;UAAC6D,OAAO,EAAE3C,cAAe;UAAAkC,QAAA,eAClC3C,OAAA,CAACH,OAAO;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACb;QAAAL,QAAA,EACEpC;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACEhD,OAAA,CAACjB,GAAG;IAACuD,EAAE,EAAE;MAAEW,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,eAChB3C,OAAA,CAACF,MAAM,CAACuD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAhB,QAAA,gBAG9B3C,OAAA,CAACjB,GAAG;QAACuD,EAAE,EAAE;UAAEsB,EAAE,EAAE,CAAC;UAAErB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACzF3C,OAAA,CAACjB,GAAG;UAAA4D,QAAA,gBACF3C,OAAA,CAAChB,UAAU;YAAC6E,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAnB,QAAA,EAAC;UAEtC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhD,OAAA,CAAChB,UAAU;YAAC6E,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,eAAe;YAAApB,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNhD,OAAA,CAACR,OAAO;UAACwE,KAAK,EAAC,mCAAiB;UAAArB,QAAA,eAC9B3C,OAAA,CAACT,UAAU;YAAC6D,OAAO,EAAE3C,cAAe;YAAAkC,QAAA,eAClC3C,OAAA,CAACH,OAAO;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGNhD,OAAA,CAACf,IAAI;QAACgF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC5B,EAAE,EAAE;UAAEsB,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,gBACxC3C,OAAA,CAACf,IAAI;UAACkF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eAC9B3C,OAAA,CAACF,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAA5B,QAAA,eAE3B3C,OAAA,CAACd,IAAI;cAAAyD,QAAA,eACH3C,OAAA,CAACb,WAAW;gBAAAwD,QAAA,gBACV3C,OAAA,CAACjB,GAAG;kBAACuD,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEmB,EAAE,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,gBACxD3C,OAAA,CAACV,MAAM;oBAACgD,EAAE,EAAE;sBAAEkC,OAAO,EAAE,cAAc;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAA9B,QAAA,eAC7C3C,OAAA,CAACP,WAAW;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACThD,OAAA,CAAChB,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAAAlB,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNhD,OAAA,CAAChB,UAAU;kBAAC6E,OAAO,EAAC,IAAI;kBAACa,SAAS,EAAC,KAAK;kBAAA/B,QAAA,EACrCxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,CAACC;gBAAc;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACbhD,OAAA,CAAChB,UAAU;kBAAC6E,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAApB,QAAA,EAAC;gBAElD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEPhD,OAAA,CAACf,IAAI;UAACkF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eAC9B3C,OAAA,CAACF,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAA5B,QAAA,eAE3B3C,OAAA,CAACd,IAAI;cAAAyD,QAAA,eACH3C,OAAA,CAACb,WAAW;gBAAAwD,QAAA,gBACV3C,OAAA,CAACjB,GAAG;kBAACuD,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEmB,EAAE,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,gBACxD3C,OAAA,CAACV,MAAM;oBAACgD,EAAE,EAAE;sBAAEkC,OAAO,EAAE,gBAAgB;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAA9B,QAAA,eAC/C3C,OAAA,CAACN,MAAM;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACThD,OAAA,CAAChB,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAAAlB,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNhD,OAAA,CAAChB,UAAU;kBAAC6E,OAAO,EAAC,IAAI;kBAACa,SAAS,EAAC,KAAK;kBAAA/B,QAAA,EACrCxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,CAACE;gBAAY;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACbhD,OAAA,CAAChB,UAAU;kBAAC6E,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAApB,QAAA,EAAC;gBAElD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEPhD,OAAA,CAACf,IAAI;UAACkF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eAC9B3C,OAAA,CAACF,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAA5B,QAAA,eAE3B3C,OAAA,CAACd,IAAI;cAAAyD,QAAA,eACH3C,OAAA,CAACb,WAAW;gBAAAwD,QAAA,gBACV3C,OAAA,CAACjB,GAAG;kBAACuD,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEmB,EAAE,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,gBACxD3C,OAAA,CAACV,MAAM;oBAACgD,EAAE,EAAE;sBAAEkC,OAAO,EAAE,cAAc;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAA9B,QAAA,eAC7C3C,OAAA,CAACJ,OAAO;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACThD,OAAA,CAAChB,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAAAlB,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNhD,OAAA,CAAChB,UAAU;kBAAC6E,OAAO,EAAC,IAAI;kBAACa,SAAS,EAAC,KAAK;kBAAA/B,QAAA,EACrChB,WAAW,CAAC,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,CAACG,gBAAgB,KAAI,CAAC;gBAAC;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACbhD,OAAA,CAAChB,UAAU;kBAAC6E,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAApB,QAAA,EAAC;gBAElD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEPhD,OAAA,CAACf,IAAI;UAACkF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eAC9B3C,OAAA,CAACF,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAA5B,QAAA,eAE3B3C,OAAA,CAACd,IAAI;cAAAyD,QAAA,eACH3C,OAAA,CAACb,WAAW;gBAAAwD,QAAA,gBACV3C,OAAA,CAACjB,GAAG;kBAACuD,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEmB,EAAE,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,gBACxD3C,OAAA,CAACV,MAAM;oBAACgD,EAAE,EAAE;sBAAEkC,OAAO,EAAE,cAAc;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAA9B,QAAA,eAC7C3C,OAAA,CAACL,IAAI;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACThD,OAAA,CAAChB,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAAAlB,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNhD,OAAA,CAAChB,UAAU;kBAAC6E,OAAO,EAAC,IAAI;kBAACa,SAAS,EAAC,KAAK;kBAAA/B,QAAA,GACrCxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,CAACI,aAAa,EAAC,IAChC;gBAAA;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbhD,OAAA,CAAChB,UAAU;kBAAC6E,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAApB,QAAA,EAAC;gBAElD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPhD,OAAA,CAACf,IAAI;QAACgF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBACzB3C,OAAA,CAACf,IAAI;UAACkF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvB3C,OAAA,CAACd,IAAI;YAAAyD,QAAA,eACH3C,OAAA,CAACb,WAAW;cAAAwD,QAAA,gBACV3C,OAAA,CAAChB,UAAU;gBAAC6E,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAnB,QAAA,EAAC;cAEtC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhD,OAAA,CAAC2E,IAAI;gBAACxE,IAAI,EAAEyE,aAAc;gBAACC,OAAO,EAAEC;cAAa;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPhD,OAAA,CAACf,IAAI;UAACkF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvB3C,OAAA,CAACd,IAAI;YAAAyD,QAAA,eACH3C,OAAA,CAACb,WAAW;cAAAwD,QAAA,gBACV3C,OAAA,CAAChB,UAAU;gBAAC6E,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAnB,QAAA,EAAC;cAEtC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhD,OAAA,CAAC+E,QAAQ;gBAAC5E,IAAI,EAAE6E;cAAa;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC9C,EAAA,CArPID,SAAmB;AAAAgF,EAAA,GAAnBhF,SAAmB;AAuPzB,eAAeA,SAAS;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}