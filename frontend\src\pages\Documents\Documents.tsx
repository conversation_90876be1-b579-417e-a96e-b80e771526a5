import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Snackbar,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Description as DocumentIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { documentAPI } from '../../services/api';

interface Document {
  id: string;
  title: string;
  originalName: string;
  fileName: string;
  mimeType: string;
  size: number;
  category: string;
  status: 'uploading' | 'processing' | 'processed' | 'error';
  createdAt: string;
  updatedAt: string;
  extractedText?: string;
  metadata?: {
    processingTime?: number;
    language?: string;
    confidence?: number;
    pageCount?: number;
  };
}

const Documents: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentTitle, setDocumentTitle] = useState('');
  const [documentDescription, setDocumentDescription] = useState('');
  const [documentCategory, setDocumentCategory] = useState('other');
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Load documents on component mount
  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const response = await documentAPI.list();
      setDocuments(response.data.documents || []);
    } catch (error) {
      console.error('Failed to load documents:', error);
      setError('Không thể tải danh sách tài liệu');
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = () => {
    setUploadDialogOpen(true);
    setSelectedFile(null);
    setDocumentTitle('');
    setDocumentDescription('');
    setDocumentCategory('other');
    setError(null);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setDocumentTitle(file.name.replace(/\.[^/.]+$/, '')); // Remove extension
    }
  };

  const handleUploadSubmit = async () => {
    if (!selectedFile) return;

    setUploading(true);
    setError(null);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('documents', selectedFile);
      formData.append('title', documentTitle || selectedFile.name);
      formData.append('description', documentDescription);
      formData.append('category', documentCategory);

      // Upload document
      const response = await documentAPI.upload(formData);

      if (response.data.success) {
        setSuccessMessage('Tài liệu đã được tải lên thành công!');
        setUploadDialogOpen(false);

        // Reload documents list
        await loadDocuments();
      } else {
        throw new Error(response.data.error || 'Upload failed');
      }

    } catch (error: any) {
      console.error('Upload failed:', error);
      setError(error.response?.data?.error || 'Không thể tải lên tài liệu. Vui lòng thử lại.');
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDelete = async (id: string) => {
    try {
      await documentAPI.delete(id);
      setSuccessMessage('Tài liệu đã được xóa thành công!');
      await loadDocuments();
    } catch (error: any) {
      console.error('Delete failed:', error);
      setError(error.response?.data?.error || 'Không thể xóa tài liệu. Vui lòng thử lại.');
    }
  };

  const getStatusColor = (status: Document['status']) => {
    switch (status) {
      case 'processed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'uploading':
        return 'info';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: Document['status']) => {
    switch (status) {
      case 'processed':
        return 'Hoàn thành';
      case 'processing':
        return 'Đang xử lý';
      case 'uploading':
        return 'Đang tải lên';
      case 'error':
        return 'Lỗi';
      default:
        return 'Không xác định';
    }
  };

  const getCategoryText = (category: string) => {
    const categories: Record<string, string> = {
      civil: 'Dân sự',
      criminal: 'Hình sự',
      administrative: 'Hành chính',
      commercial: 'Thương mại',
      labor: 'Lao động',
      other: 'Khác'
    };
    return categories[category] || category;
  };

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <div>
            <Typography variant="h4" gutterBottom>
              Tài liệu
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Quản lý và phân tích tài liệu pháp luật
            </Typography>
          </div>
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={handleUpload}
            size="large"
          >
            Tải lên tài liệu
          </Button>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : documents.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <DocumentIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Chưa có tài liệu nào
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Tải lên tài liệu đầu tiên để bắt đầu
            </Typography>
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={handleUpload}
            >
              Tải lên tài liệu
            </Button>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {documents.map((document, index) => (
              <Grid item xs={12} sm={6} md={4} key={document.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card sx={{ height: '100%' }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <DocumentIcon color="primary" sx={{ mr: 1 }} />
                        <Typography variant="h6" noWrap title={document.title}>
                          {document.title}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Loại: {document.mimeType.split('/')[1]?.toUpperCase()} • Kích thước: {formatFileSize(document.size)}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Danh mục: {getCategoryText(document.category)}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Ngày tải: {new Date(document.createdAt).toLocaleDateString('vi-VN')}
                      </Typography>
                      <Chip
                        label={getStatusText(document.status)}
                        color={getStatusColor(document.status)}
                        size="small"
                      />
                      {document.metadata?.pageCount && (
                        <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                          {document.metadata.pageCount} trang
                        </Typography>
                      )}
                    </CardContent>
                    <CardActions>
                      <IconButton size="small" color="primary">
                        <ViewIcon />
                      </IconButton>
                      <IconButton size="small" color="primary">
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDelete(document.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </CardActions>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Upload Dialog */}
        <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Tải lên tài liệu mới</DialogTitle>
          <DialogContent>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            <Box sx={{ mt: 2 }}>
              <TextField
                fullWidth
                label="Tên tài liệu"
                variant="outlined"
                value={documentTitle}
                onChange={(e) => setDocumentTitle(e.target.value)}
                sx={{ mb: 2 }}
                required
              />
              <TextField
                fullWidth
                label="Mô tả (tùy chọn)"
                variant="outlined"
                multiline
                rows={2}
                value={documentDescription}
                onChange={(e) => setDocumentDescription(e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                select
                label="Danh mục"
                value={documentCategory}
                onChange={(e) => setDocumentCategory(e.target.value)}
                sx={{ mb: 2 }}
                SelectProps={{
                  native: true,
                }}
              >
                <option value="civil">Dân sự</option>
                <option value="criminal">Hình sự</option>
                <option value="administrative">Hành chính</option>
                <option value="commercial">Thương mại</option>
                <option value="labor">Lao động</option>
                <option value="other">Khác</option>
              </TextField>
              <Button
                variant="outlined"
                component="label"
                fullWidth
                sx={{
                  height: 100,
                  borderStyle: 'dashed',
                  backgroundColor: selectedFile ? 'action.hover' : 'transparent'
                }}
              >
                <Box sx={{ textAlign: 'center' }}>
                  <UploadIcon sx={{ fontSize: 40, mb: 1 }} />
                  <Typography>
                    {selectedFile ? selectedFile.name : 'Chọn tệp hoặc kéo thả vào đây'}
                  </Typography>
                  {selectedFile && (
                    <Typography variant="caption" color="textSecondary">
                      {formatFileSize(selectedFile.size)}
                    </Typography>
                  )}
                </Box>
                <input
                  type="file"
                  hidden
                  accept=".pdf,.doc,.docx,.txt"
                  onChange={handleFileSelect}
                />
              </Button>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUploadDialogOpen(false)} disabled={uploading}>
              Hủy
            </Button>
            <Button
              variant="contained"
              onClick={handleUploadSubmit}
              disabled={!selectedFile || uploading || !documentTitle.trim()}
              startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
            >
              {uploading ? 'Đang tải lên...' : 'Tải lên'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Success/Error Snackbars */}
        <Snackbar
          open={!!successMessage}
          autoHideDuration={6000}
          onClose={() => setSuccessMessage(null)}
        >
          <Alert onClose={() => setSuccessMessage(null)} severity="success">
            {successMessage}
          </Alert>
        </Snackbar>

        <Snackbar
          open={!!error}
          autoHideDuration={6000}
          onClose={() => setError(null)}
        >
          <Alert onClose={() => setError(null)} severity="error">
            {error}
          </Alert>
        </Snackbar>
      </motion.div>
    </Box>
  );
};

export default Documents;
