{"ast": null, "code": "var _jsxFileName = \"E:\\\\Project\\\\AI\\\\RAG\\\\Law\\\\LegalMind Pro - AI Assistant Ph\\xE1p Lu\\u1EADt Th\\xF4ng Minh\\\\frontend\\\\src\\\\pages\\\\Documents\\\\Documents.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Grid, Card, CardContent, CardActions, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField } from '@mui/material';\nimport { CloudUpload as UploadIcon, Description as DocumentIcon, Delete as DeleteIcon, Visibility as ViewIcon, Edit as EditIcon } from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Documents = () => {\n  _s();\n  const [documents, setDocuments] = useState([{\n    id: '1',\n    name: '<PERSON><PERSON><PERSON> 2020.pdf',\n    type: 'PDF',\n    size: '2.5 MB',\n    uploadDate: '2024-01-15',\n    status: 'completed'\n  }, {\n    id: '2',\n    name: '<PERSON><PERSON> lu<PERSON>t Dân sự 2015.docx',\n    type: 'DOCX',\n    size: '1.8 MB',\n    uploadDate: '2024-01-14',\n    status: 'completed'\n  }]);\n  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [documentTitle, setDocumentTitle] = useState('');\n  const [uploading, setUploading] = useState(false);\n  const handleUpload = () => {\n    setUploadDialogOpen(true);\n    setSelectedFile(null);\n    setDocumentTitle('');\n  };\n  const handleFileSelect = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      setSelectedFile(file);\n      setDocumentTitle(file.name.replace(/\\.[^/.]+$/, '')); // Remove extension\n    }\n  };\n  const handleUploadSubmit = async () => {\n    if (!selectedFile) return;\n    setUploading(true);\n    try {\n      var _selectedFile$name$sp;\n      // Simulate upload process\n      const newDocument = {\n        id: Date.now().toString(),\n        name: documentTitle || selectedFile.name,\n        type: ((_selectedFile$name$sp = selectedFile.name.split('.').pop()) === null || _selectedFile$name$sp === void 0 ? void 0 : _selectedFile$name$sp.toUpperCase()) || 'UNKNOWN',\n        size: formatFileSize(selectedFile.size),\n        uploadDate: new Date().toISOString().split('T')[0],\n        status: 'processing'\n      };\n      setDocuments(prev => [newDocument, ...prev]);\n      setUploadDialogOpen(false);\n\n      // Simulate processing completion\n      setTimeout(() => {\n        setDocuments(prev => prev.map(doc => doc.id === newDocument.id ? {\n          ...doc,\n          status: 'completed'\n        } : doc));\n      }, 3000);\n    } catch (error) {\n      console.error('Upload failed:', error);\n    } finally {\n      setUploading(false);\n    }\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const handleDelete = id => {\n    setDocuments(documents.filter(doc => doc.id !== id));\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'processing':\n        return 'warning';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'completed':\n        return 'Hoàn thành';\n      case 'processing':\n        return 'Đang xử lý';\n      case 'error':\n        return 'Lỗi';\n      default:\n        return 'Không xác định';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            gutterBottom: true,\n            children: \"T\\xE0i li\\u1EC7u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"textSecondary\",\n            children: \"Qu\\u1EA3n l\\xFD v\\xE0 ph\\xE2n t\\xEDch t\\xE0i li\\u1EC7u ph\\xE1p lu\\u1EADt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(UploadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 24\n          }, this),\n          onClick: handleUpload,\n          size: \"large\",\n          children: \"T\\u1EA3i l\\xEAn t\\xE0i li\\u1EC7u\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: documents.map((document, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(DocumentIcon, {\n                    color: \"primary\",\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    noWrap: true,\n                    children: document.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: [\"Lo\\u1EA1i: \", document.type, \" \\u2022 K\\xEDch th\\u01B0\\u1EDBc: \", document.size]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: [\"Ng\\xE0y t\\u1EA3i: \", document.uploadDate]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: getStatusText(document.status),\n                  color: getStatusColor(document.status),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  color: \"error\",\n                  onClick: () => handleDelete(document.id),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)\n        }, document.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: uploadDialogOpen,\n        onClose: () => setUploadDialogOpen(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"T\\u1EA3i l\\xEAn t\\xE0i li\\u1EC7u m\\u1EDBi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"T\\xEAn t\\xE0i li\\u1EC7u\",\n              variant: \"outlined\",\n              value: documentTitle,\n              onChange: e => setDocumentTitle(e.target.value),\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              component: \"label\",\n              fullWidth: true,\n              sx: {\n                height: 100,\n                borderStyle: 'dashed',\n                backgroundColor: selectedFile ? 'action.hover' : 'transparent'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(UploadIcon, {\n                  sx: {\n                    fontSize: 40,\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: selectedFile ? selectedFile.name : 'Chọn tệp hoặc kéo thả vào đây'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), selectedFile && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"textSecondary\",\n                  children: formatFileSize(selectedFile.size)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                hidden: true,\n                accept: \".pdf,.doc,.docx,.txt\",\n                onChange: handleFileSelect\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setUploadDialogOpen(false),\n            disabled: uploading,\n            children: \"H\\u1EE7y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleUploadSubmit,\n            disabled: !selectedFile || uploading,\n            children: uploading ? 'Đang tải lên...' : 'Tải lên'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(Documents, \"LjU3c+9jD/N2+SanI8v7sB4OUrA=\");\n_c = Documents;\nexport default Documents;\nvar _c;\n$RefreshReg$(_c, \"Documents\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "CloudUpload", "UploadIcon", "Description", "DocumentIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Edit", "EditIcon", "motion", "jsxDEV", "_jsxDEV", "Documents", "_s", "documents", "setDocuments", "id", "name", "type", "size", "uploadDate", "status", "uploadDialogOpen", "setUploadDialogOpen", "selectedFile", "setSelectedFile", "documentTitle", "setDocumentTitle", "uploading", "setUploading", "handleUpload", "handleFileSelect", "event", "_event$target$files", "file", "target", "files", "replace", "handleUploadSubmit", "_selectedFile$name$sp", "newDocument", "Date", "now", "toString", "split", "pop", "toUpperCase", "formatFileSize", "toISOString", "prev", "setTimeout", "map", "doc", "error", "console", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "handleDelete", "filter", "getStatusColor", "getStatusText", "sx", "p", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "display", "justifyContent", "alignItems", "mb", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "startIcon", "onClick", "container", "spacing", "document", "index", "item", "xs", "sm", "md", "delay", "height", "mr", "noWrap", "label", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "mt", "value", "onChange", "e", "component", "borderStyle", "backgroundColor", "textAlign", "fontSize", "hidden", "accept", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/Project/AI/RAG/Law/LegalMind Pro - AI Assistant <PERSON><PERSON><PERSON>/frontend/src/pages/Documents/Documents.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  <PERSON>po<PERSON>,\n  <PERSON>ton,\n  Grid,\n  Card,\n  CardContent,\n  Card<PERSON>ctions,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n} from '@mui/material';\nimport {\n  CloudUpload as UploadIcon,\n  Description as DocumentIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Edit as EditIcon,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\n\ninterface Document {\n  id: string;\n  name: string;\n  type: string;\n  size: string;\n  uploadDate: string;\n  status: 'processing' | 'completed' | 'error';\n}\n\nconst Documents: React.FC = () => {\n  const [documents, setDocuments] = useState<Document[]>([\n    {\n      id: '1',\n      name: '<PERSON><PERSON><PERSON> nghiệp 2020.pdf',\n      type: 'PDF',\n      size: '2.5 MB',\n      uploadDate: '2024-01-15',\n      status: 'completed',\n    },\n    {\n      id: '2',\n      name: '<PERSON><PERSON> <PERSON>u<PERSON><PERSON> sự 2015.docx',\n      type: 'DOCX',\n      size: '1.8 MB',\n      uploadDate: '2024-01-14',\n      status: 'completed',\n    },\n  ]);\n\n  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [documentTitle, setDocumentTitle] = useState('');\n  const [uploading, setUploading] = useState(false);\n\n  const handleUpload = () => {\n    setUploadDialogOpen(true);\n    setSelectedFile(null);\n    setDocumentTitle('');\n  };\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setSelectedFile(file);\n      setDocumentTitle(file.name.replace(/\\.[^/.]+$/, '')); // Remove extension\n    }\n  };\n\n  const handleUploadSubmit = async () => {\n    if (!selectedFile) return;\n\n    setUploading(true);\n    try {\n      // Simulate upload process\n      const newDocument: Document = {\n        id: Date.now().toString(),\n        name: documentTitle || selectedFile.name,\n        type: selectedFile.name.split('.').pop()?.toUpperCase() || 'UNKNOWN',\n        size: formatFileSize(selectedFile.size),\n        uploadDate: new Date().toISOString().split('T')[0],\n        status: 'processing',\n      };\n\n      setDocuments(prev => [newDocument, ...prev]);\n      setUploadDialogOpen(false);\n\n      // Simulate processing completion\n      setTimeout(() => {\n        setDocuments(prev =>\n          prev.map(doc =>\n            doc.id === newDocument.id\n              ? { ...doc, status: 'completed' as const }\n              : doc\n          )\n        );\n      }, 3000);\n\n    } catch (error) {\n      console.error('Upload failed:', error);\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const handleDelete = (id: string) => {\n    setDocuments(documents.filter(doc => doc.id !== id));\n  };\n\n  const getStatusColor = (status: Document['status']) => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'processing':\n        return 'warning';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const getStatusText = (status: Document['status']) => {\n    switch (status) {\n      case 'completed':\n        return 'Hoàn thành';\n      case 'processing':\n        return 'Đang xử lý';\n      case 'error':\n        return 'Lỗi';\n      default:\n        return 'Không xác định';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>\n          <div>\n            <Typography variant=\"h4\" gutterBottom>\n              Tài liệu\n            </Typography>\n            <Typography variant=\"body1\" color=\"textSecondary\">\n              Quản lý và phân tích tài liệu pháp luật\n            </Typography>\n          </div>\n          <Button\n            variant=\"contained\"\n            startIcon={<UploadIcon />}\n            onClick={handleUpload}\n            size=\"large\"\n          >\n            Tải lên tài liệu\n          </Button>\n        </Box>\n\n        <Grid container spacing={3}>\n          {documents.map((document, index) => (\n            <Grid item xs={12} sm={6} md={4} key={document.id}>\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n              >\n                <Card sx={{ height: '100%' }}>\n                  <CardContent>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <DocumentIcon color=\"primary\" sx={{ mr: 1 }} />\n                      <Typography variant=\"h6\" noWrap>\n                        {document.name}\n                      </Typography>\n                    </Box>\n                    <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n                      Loại: {document.type} • Kích thước: {document.size}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n                      Ngày tải: {document.uploadDate}\n                    </Typography>\n                    <Chip\n                      label={getStatusText(document.status)}\n                      color={getStatusColor(document.status)}\n                      size=\"small\"\n                    />\n                  </CardContent>\n                  <CardActions>\n                    <IconButton size=\"small\" color=\"primary\">\n                      <ViewIcon />\n                    </IconButton>\n                    <IconButton size=\"small\" color=\"primary\">\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton\n                      size=\"small\"\n                      color=\"error\"\n                      onClick={() => handleDelete(document.id)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </CardActions>\n                </Card>\n              </motion.div>\n            </Grid>\n          ))}\n        </Grid>\n\n        {/* Upload Dialog */}\n        <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Tải lên tài liệu mới</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <TextField\n                fullWidth\n                label=\"Tên tài liệu\"\n                variant=\"outlined\"\n                value={documentTitle}\n                onChange={(e) => setDocumentTitle(e.target.value)}\n                sx={{ mb: 2 }}\n              />\n              <Button\n                variant=\"outlined\"\n                component=\"label\"\n                fullWidth\n                sx={{\n                  height: 100,\n                  borderStyle: 'dashed',\n                  backgroundColor: selectedFile ? 'action.hover' : 'transparent'\n                }}\n              >\n                <Box sx={{ textAlign: 'center' }}>\n                  <UploadIcon sx={{ fontSize: 40, mb: 1 }} />\n                  <Typography>\n                    {selectedFile ? selectedFile.name : 'Chọn tệp hoặc kéo thả vào đây'}\n                  </Typography>\n                  {selectedFile && (\n                    <Typography variant=\"caption\" color=\"textSecondary\">\n                      {formatFileSize(selectedFile.size)}\n                    </Typography>\n                  )}\n                </Box>\n                <input\n                  type=\"file\"\n                  hidden\n                  accept=\".pdf,.doc,.docx,.txt\"\n                  onChange={handleFileSelect}\n                />\n              </Button>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setUploadDialogOpen(false)} disabled={uploading}>\n              Hủy\n            </Button>\n            <Button\n              variant=\"contained\"\n              onClick={handleUploadSubmit}\n              disabled={!selectedFile || uploading}\n            >\n              {uploading ? 'Đang tải lên...' : 'Tải lên'}\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </motion.div>\n    </Box>\n  );\n};\n\nexport default Documents;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,QACJ,eAAe;AACtB,SACEC,WAAW,IAAIC,UAAU,EACzBC,WAAW,IAAIC,YAAY,EAC3BC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWvC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAa,CACrD;IACEgC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,4BAA4B;IAClCC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,QAAQ;IACdC,UAAU,EAAE,YAAY;IACxBC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,0BAA0B;IAChCC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,QAAQ;IACdC,UAAU,EAAE,YAAY;IACxBC,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM8C,YAAY,GAAGA,CAAA,KAAM;IACzBP,mBAAmB,CAAC,IAAI,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;IACrBE,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAMI,gBAAgB,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACvE,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MACRT,eAAe,CAACS,IAAI,CAAC;MACrBP,gBAAgB,CAACO,IAAI,CAACjB,IAAI,CAACoB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACd,YAAY,EAAE;IAEnBK,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MAAA,IAAAU,qBAAA;MACF;MACA,MAAMC,WAAqB,GAAG;QAC5BxB,EAAE,EAAEyB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzB1B,IAAI,EAAES,aAAa,IAAIF,YAAY,CAACP,IAAI;QACxCC,IAAI,EAAE,EAAAqB,qBAAA,GAAAf,YAAY,CAACP,IAAI,CAAC2B,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAN,qBAAA,uBAAlCA,qBAAA,CAAoCO,WAAW,CAAC,CAAC,KAAI,SAAS;QACpE3B,IAAI,EAAE4B,cAAc,CAACvB,YAAY,CAACL,IAAI,CAAC;QACvCC,UAAU,EAAE,IAAIqB,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDvB,MAAM,EAAE;MACV,CAAC;MAEDN,YAAY,CAACkC,IAAI,IAAI,CAACT,WAAW,EAAE,GAAGS,IAAI,CAAC,CAAC;MAC5C1B,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACA2B,UAAU,CAAC,MAAM;QACfnC,YAAY,CAACkC,IAAI,IACfA,IAAI,CAACE,GAAG,CAACC,GAAG,IACVA,GAAG,CAACpC,EAAE,KAAKwB,WAAW,CAACxB,EAAE,GACrB;UAAE,GAAGoC,GAAG;UAAE/B,MAAM,EAAE;QAAqB,CAAC,GACxC+B,GACN,CACF,CAAC;MACH,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC,CAAC,SAAS;MACRxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkB,cAAc,GAAIQ,KAAa,IAAa;IAChD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,YAAY,GAAIjD,EAAU,IAAK;IACnCD,YAAY,CAACD,SAAS,CAACoD,MAAM,CAACd,GAAG,IAAIA,GAAG,CAACpC,EAAE,KAAKA,EAAE,CAAC,CAAC;EACtD,CAAC;EAED,MAAMmD,cAAc,GAAI9C,MAA0B,IAAK;IACrD,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAM+C,aAAa,GAAI/C,MAA0B,IAAK;IACpD,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,YAAY;MACrB,KAAK,YAAY;QACf,OAAO,YAAY;MACrB,KAAK,OAAO;QACV,OAAO,KAAK;MACd;QACE,OAAO,gBAAgB;IAC3B;EACF,CAAC;EAED,oBACEV,OAAA,CAAC1B,GAAG;IAACoF,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,eAChB5D,OAAA,CAACF,MAAM,CAAC+D,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAP,QAAA,gBAE9B5D,OAAA,CAAC1B,GAAG;QAACoF,EAAE,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzF5D,OAAA;UAAA4D,QAAA,gBACE5D,OAAA,CAACzB,UAAU;YAACiG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAb,QAAA,EAAC;UAEtC;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7E,OAAA,CAACzB,UAAU;YAACiG,OAAO,EAAC,OAAO;YAACM,KAAK,EAAC,eAAe;YAAAlB,QAAA,EAAC;UAElD;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN7E,OAAA,CAACxB,MAAM;UACLgG,OAAO,EAAC,WAAW;UACnBO,SAAS,eAAE/E,OAAA,CAACX,UAAU;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BG,OAAO,EAAE7D,YAAa;UACtBX,IAAI,EAAC,OAAO;UAAAoD,QAAA,EACb;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7E,OAAA,CAACvB,IAAI;QAACwG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAtB,QAAA,EACxBzD,SAAS,CAACqC,GAAG,CAAC,CAAC2C,QAAQ,EAAEC,KAAK,kBAC7BpF,OAAA,CAACvB,IAAI;UAAC4G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9B5D,OAAA,CAACF,MAAM,CAAC+D,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEsB,KAAK,EAAEL,KAAK,GAAG;YAAI,CAAE;YAAAxB,QAAA,eAElD5D,OAAA,CAACtB,IAAI;cAACgF,EAAE,EAAE;gBAAEgC,MAAM,EAAE;cAAO,CAAE;cAAA9B,QAAA,gBAC3B5D,OAAA,CAACrB,WAAW;gBAAAiF,QAAA,gBACV5D,OAAA,CAAC1B,GAAG;kBAACoF,EAAE,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAAX,QAAA,gBACxD5D,OAAA,CAACT,YAAY;oBAACuF,KAAK,EAAC,SAAS;oBAACpB,EAAE,EAAE;sBAAEiC,EAAE,EAAE;oBAAE;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/C7E,OAAA,CAACzB,UAAU;oBAACiG,OAAO,EAAC,IAAI;oBAACoB,MAAM;oBAAAhC,QAAA,EAC5BuB,QAAQ,CAAC7E;kBAAI;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN7E,OAAA,CAACzB,UAAU;kBAACiG,OAAO,EAAC,OAAO;kBAACM,KAAK,EAAC,eAAe;kBAACL,YAAY;kBAAAb,QAAA,GAAC,aACvD,EAACuB,QAAQ,CAAC5E,IAAI,EAAC,mCAAe,EAAC4E,QAAQ,CAAC3E,IAAI;gBAAA;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACb7E,OAAA,CAACzB,UAAU;kBAACiG,OAAO,EAAC,OAAO;kBAACM,KAAK,EAAC,eAAe;kBAACL,YAAY;kBAAAb,QAAA,GAAC,oBACnD,EAACuB,QAAQ,CAAC1E,UAAU;gBAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACb7E,OAAA,CAAClB,IAAI;kBACH+G,KAAK,EAAEpC,aAAa,CAAC0B,QAAQ,CAACzE,MAAM,CAAE;kBACtCoE,KAAK,EAAEtB,cAAc,CAAC2B,QAAQ,CAACzE,MAAM,CAAE;kBACvCF,IAAI,EAAC;gBAAO;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eACd7E,OAAA,CAACpB,WAAW;gBAAAgF,QAAA,gBACV5D,OAAA,CAACnB,UAAU;kBAAC2B,IAAI,EAAC,OAAO;kBAACsE,KAAK,EAAC,SAAS;kBAAAlB,QAAA,eACtC5D,OAAA,CAACL,QAAQ;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACb7E,OAAA,CAACnB,UAAU;kBAAC2B,IAAI,EAAC,OAAO;kBAACsE,KAAK,EAAC,SAAS;kBAAAlB,QAAA,eACtC5D,OAAA,CAACH,QAAQ;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACb7E,OAAA,CAACnB,UAAU;kBACT2B,IAAI,EAAC,OAAO;kBACZsE,KAAK,EAAC,OAAO;kBACbE,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAAC6B,QAAQ,CAAC9E,EAAE,CAAE;kBAAAuD,QAAA,eAEzC5D,OAAA,CAACP,UAAU;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC,GA1CuBM,QAAQ,CAAC9E,EAAE;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2C3C,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGP7E,OAAA,CAACjB,MAAM;QAAC+G,IAAI,EAAEnF,gBAAiB;QAACoF,OAAO,EAAEA,CAAA,KAAMnF,mBAAmB,CAAC,KAAK,CAAE;QAACoF,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAArC,QAAA,gBAChG5D,OAAA,CAAChB,WAAW;UAAA4E,QAAA,EAAC;QAAoB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC/C7E,OAAA,CAACf,aAAa;UAAA2E,QAAA,eACZ5D,OAAA,CAAC1B,GAAG;YAACoF,EAAE,EAAE;cAAEwC,EAAE,EAAE;YAAE,CAAE;YAAAtC,QAAA,gBACjB5D,OAAA,CAACb,SAAS;cACR8G,SAAS;cACTJ,KAAK,EAAC,yBAAc;cACpBrB,OAAO,EAAC,UAAU;cAClB2B,KAAK,EAAEpF,aAAc;cACrBqF,QAAQ,EAAGC,CAAC,IAAKrF,gBAAgB,CAACqF,CAAC,CAAC7E,MAAM,CAAC2E,KAAK,CAAE;cAClDzC,EAAE,EAAE;gBAAEa,EAAE,EAAE;cAAE;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACF7E,OAAA,CAACxB,MAAM;cACLgG,OAAO,EAAC,UAAU;cAClB8B,SAAS,EAAC,OAAO;cACjBL,SAAS;cACTvC,EAAE,EAAE;gBACFgC,MAAM,EAAE,GAAG;gBACXa,WAAW,EAAE,QAAQ;gBACrBC,eAAe,EAAE3F,YAAY,GAAG,cAAc,GAAG;cACnD,CAAE;cAAA+C,QAAA,gBAEF5D,OAAA,CAAC1B,GAAG;gBAACoF,EAAE,EAAE;kBAAE+C,SAAS,EAAE;gBAAS,CAAE;gBAAA7C,QAAA,gBAC/B5D,OAAA,CAACX,UAAU;kBAACqE,EAAE,EAAE;oBAAEgD,QAAQ,EAAE,EAAE;oBAAEnC,EAAE,EAAE;kBAAE;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3C7E,OAAA,CAACzB,UAAU;kBAAAqF,QAAA,EACR/C,YAAY,GAAGA,YAAY,CAACP,IAAI,GAAG;gBAA+B;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,EACZhE,YAAY,iBACXb,OAAA,CAACzB,UAAU;kBAACiG,OAAO,EAAC,SAAS;kBAACM,KAAK,EAAC,eAAe;kBAAAlB,QAAA,EAChDxB,cAAc,CAACvB,YAAY,CAACL,IAAI;gBAAC;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN7E,OAAA;gBACEO,IAAI,EAAC,MAAM;gBACXoG,MAAM;gBACNC,MAAM,EAAC,sBAAsB;gBAC7BR,QAAQ,EAAEhF;cAAiB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB7E,OAAA,CAACd,aAAa;UAAA0E,QAAA,gBACZ5D,OAAA,CAACxB,MAAM;YAACwG,OAAO,EAAEA,CAAA,KAAMpE,mBAAmB,CAAC,KAAK,CAAE;YAACiG,QAAQ,EAAE5F,SAAU;YAAA2C,QAAA,EAAC;UAExE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7E,OAAA,CAACxB,MAAM;YACLgG,OAAO,EAAC,WAAW;YACnBQ,OAAO,EAAErD,kBAAmB;YAC5BkF,QAAQ,EAAE,CAAChG,YAAY,IAAII,SAAU;YAAA2C,QAAA,EAEpC3C,SAAS,GAAG,iBAAiB,GAAG;UAAS;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAvPID,SAAmB;AAAA6G,EAAA,GAAnB7G,SAAmB;AAyPzB,eAAeA,SAAS;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}