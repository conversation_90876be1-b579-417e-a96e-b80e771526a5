import { db, queryHelper } from '../config/database';
import { logger } from '../utils/logger';

export interface DocumentEmbedding {
  id?: string;
  documentId: string;
  chunkIndex: number;
  chunkText: string;
  embedding: number[];
  metadata: any;
}

export class EmbeddingService {
  constructor() {
    logger.info('EmbeddingService initialized');
  }

  /**
   * Store document embeddings in the database
   */
  async storeDocumentEmbeddings(documentId: string, chunks: any[], embeddings: any[]): Promise<void> {
    const startTime = Date.now();
    
    try {
      logger.info('Storing document embeddings', {
        documentId,
        chunkCount: chunks.length,
        embeddingCount: embeddings.length,
      });

      // Use transaction to ensure all embeddings are stored together
      await db.transaction(async (client) => {
        // First, delete any existing embeddings for this document
        await client.query(
          'DELETE FROM document_embeddings WHERE document_id = $1',
          [documentId]
        );

        // Insert new embeddings
        for (let i = 0; i < embeddings.length; i++) {
          const chunk = chunks[i];
          const embedding = embeddings[i];

          if (!chunk || !embedding) {
            logger.warn('Missing chunk or embedding data', { index: i, documentId });
            continue;
          }

          // Convert embedding vector to PostgreSQL array format
          const vectorString = `[${embedding.vector.join(',')}]`;

          await client.query(
            `INSERT INTO document_embeddings 
             (document_id, chunk_index, chunk_text, embedding, metadata) 
             VALUES ($1, $2, $3, $4::vector, $5)`,
            [
              documentId,
              chunk.index,
              chunk.text,
              vectorString,
              JSON.stringify({
                ...chunk.metadata,
                embeddingModel: embedding.model,
                tokensUsed: embedding.tokensUsed,
                startChar: chunk.startChar,
                endChar: chunk.endChar,
              }),
            ]
          );
        }
      });

      const processingTime = Date.now() - startTime;
      
      logger.info('Document embeddings stored successfully', {
        documentId,
        embeddingCount: embeddings.length,
        processingTime: `${processingTime}ms`,
      });

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.error('Failed to store document embeddings', {
        error: error.message,
        documentId,
        chunkCount: chunks.length,
        processingTime: `${processingTime}ms`,
      });

      throw new Error(`Failed to store embeddings: ${error.message}`);
    }
  }

  /**
   * Search for similar document chunks using vector similarity
   */
  async searchSimilarChunks(
    queryEmbedding: number[],
    options: {
      limit?: number;
      threshold?: number;
      documentIds?: string[];
      userId?: string;
    } = {}
  ): Promise<any[]> {
    const startTime = Date.now();
    const { limit = 10, threshold = 0.7, documentIds, userId } = options;

    try {
      logger.debug('Searching for similar chunks', {
        embeddingDimension: queryEmbedding.length,
        limit,
        threshold,
        documentCount: documentIds?.length,
        userId,
      });

      // Build query conditions
      let whereConditions = ['d.deleted_at IS NULL'];
      let queryParams: any[] = [];
      let paramIndex = 1;

      // Add user filter if provided
      if (userId) {
        whereConditions.push(`d.user_id = $${paramIndex}`);
        queryParams.push(userId);
        paramIndex++;
      }

      // Add document filter if provided
      if (documentIds && documentIds.length > 0) {
        whereConditions.push(`de.document_id = ANY($${paramIndex})`);
        queryParams.push(documentIds);
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');
      const vectorString = `[${queryEmbedding.join(',')}]`;

      // Query for similar embeddings using cosine similarity
      const query = `
        SELECT 
          de.id,
          de.document_id,
          de.chunk_index,
          de.chunk_text,
          de.metadata,
          d.title as document_title,
          d.original_name as document_name,
          d.category as document_category,
          (de.embedding <=> $${paramIndex}::vector) as distance,
          (1 - (de.embedding <=> $${paramIndex}::vector)) as similarity
        FROM document_embeddings de
        JOIN documents d ON de.document_id = d.id
        WHERE ${whereClause}
        ORDER BY de.embedding <=> $${paramIndex}::vector
        LIMIT $${paramIndex + 1}
      `;

      queryParams.push(vectorString, limit);

      const result = await db.query(query, queryParams);
      
      // Filter by similarity threshold
      const similarChunks = result.rows
        .filter(row => row.similarity >= threshold)
        .map(row => ({
          id: row.id,
          documentId: row.document_id,
          chunkIndex: row.chunk_index,
          text: row.chunk_text,
          metadata: row.metadata,
          document: {
            title: row.document_title,
            name: row.document_name,
            category: row.document_category,
          },
          similarity: parseFloat(row.similarity),
          distance: parseFloat(row.distance),
        }));

      const processingTime = Date.now() - startTime;
      
      logger.debug('Similar chunks search completed', {
        resultCount: similarChunks.length,
        processingTime: `${processingTime}ms`,
        avgSimilarity: similarChunks.length > 0 
          ? (similarChunks.reduce((sum, chunk) => sum + chunk.similarity, 0) / similarChunks.length).toFixed(3)
          : 0,
      });

      return similarChunks;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.error('Failed to search similar chunks', {
        error: error.message,
        embeddingDimension: queryEmbedding.length,
        processingTime: `${processingTime}ms`,
      });

      throw new Error(`Vector search failed: ${error.message}`);
    }
  }

  /**
   * Get embeddings for a specific document
   */
  async getDocumentEmbeddings(documentId: string): Promise<DocumentEmbedding[]> {
    try {
      const result = await db.query(
        'SELECT * FROM document_embeddings WHERE document_id = $1 ORDER BY chunk_index',
        [documentId]
      );

      return result.rows.map(row => ({
        id: row.id,
        documentId: row.document_id,
        chunkIndex: row.chunk_index,
        chunkText: row.chunk_text,
        embedding: row.embedding, // PostgreSQL vector type
        metadata: row.metadata,
      }));

    } catch (error) {
      logger.error('Failed to get document embeddings', {
        error: error.message,
        documentId,
      });

      throw new Error(`Failed to retrieve embeddings: ${error.message}`);
    }
  }

  /**
   * Delete embeddings for a document
   */
  async deleteDocumentEmbeddings(documentId: string): Promise<boolean> {
    try {
      const result = await db.query(
        'DELETE FROM document_embeddings WHERE document_id = $1',
        [documentId]
      );

      logger.info('Document embeddings deleted', {
        documentId,
        deletedCount: result.rowCount,
      });

      return result.rowCount > 0;

    } catch (error) {
      logger.error('Failed to delete document embeddings', {
        error: error.message,
        documentId,
      });

      throw new Error(`Failed to delete embeddings: ${error.message}`);
    }
  }

  /**
   * Get embedding statistics
   */
  async getEmbeddingStats(): Promise<any> {
    try {
      const result = await db.query(`
        SELECT 
          COUNT(*) as total_embeddings,
          COUNT(DISTINCT document_id) as documents_with_embeddings,
          AVG(array_length(embedding, 1)) as avg_embedding_dimension
        FROM document_embeddings
      `);

      return result.rows[0];

    } catch (error) {
      logger.error('Failed to get embedding statistics', {
        error: error.message,
      });

      return {
        total_embeddings: 0,
        documents_with_embeddings: 0,
        avg_embedding_dimension: 0,
      };
    }
  }
}
