import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { AIService } from './aiService';
import { EmbeddingService } from './embeddingService';
import { DocumentService } from './documentService';

export interface QueryRequest {
  query: string;
  queryType?: 'legal_advice' | 'document_search' | 'case_analysis' | 'regulation_lookup';
  context?: any;
  userId: string;
  includeDocuments?: boolean;
  documentIds?: string[];
}

export interface QueryResponse {
  queryId: string;
  response: string;
  sources: any[];
  confidence: number;
  tokensUsed: number;
  processingTime: number;
  metadata: any;
}

export class QueryService {
  private aiService: AIService;
  private embeddingService: EmbeddingService;
  private documentService: DocumentService;

  constructor() {
    this.aiService = new AIService();
    this.embeddingService = new EmbeddingService();
    this.documentService = new DocumentService();
    logger.info('QueryService initialized');
  }

  /**
   * Process a legal query with document context
   */
  async processQuery(request: QueryRequest): Promise<QueryResponse> {
    const startTime = Date.now();
    const queryId = uuidv4();

    try {
      logger.info('Processing legal query', {
        queryId,
        userId: request.userId,
        queryType: request.queryType,
        queryLength: request.query.length,
        includeDocuments: request.includeDocuments,
      });

      // Step 1: Get relevant document chunks if requested
      let documentSources: any[] = [];
      let enhancedContext = request.context || {};

      if (request.includeDocuments !== false) {
        documentSources = await this.searchRelevantDocuments(
          request.query,
          request.userId,
          request.documentIds
        );

        // Add document context to the query
        if (documentSources.length > 0) {
          enhancedContext.documentContext = documentSources.map(source => ({
            text: source.text,
            document: source.document.title,
            similarity: source.similarity,
          }));
        }
      }

      // Step 2: Process query with AI service
      const aiResponse = await this.aiService.processLegalQuery({
        query: request.query,
        context: enhancedContext,
        userId: request.userId,
        queryType: request.queryType || 'legal_advice',
      });

      // Step 3: Combine AI response with document sources
      const allSources = [
        ...aiResponse.sources,
        ...documentSources.map(source => ({
          title: source.document.title,
          content: source.text.substring(0, 200) + '...',
          confidence: source.similarity,
          type: 'document',
          documentId: source.documentId,
          chunkIndex: source.chunkIndex,
          category: source.document.category,
        })),
      ];

      const totalProcessingTime = Date.now() - startTime;

      const response: QueryResponse = {
        queryId,
        response: aiResponse.response,
        sources: allSources,
        confidence: this.calculateOverallConfidence(aiResponse.confidence, documentSources),
        tokensUsed: aiResponse.tokensUsed,
        processingTime: totalProcessingTime,
        metadata: {
          ...aiResponse.metadata,
          documentSourceCount: documentSources.length,
          queryType: request.queryType,
          timestamp: new Date().toISOString(),
        },
      };

      logger.info('Query processed successfully', {
        queryId,
        userId: request.userId,
        processingTime: totalProcessingTime,
        sourceCount: allSources.length,
        documentSourceCount: documentSources.length,
        confidence: response.confidence,
      });

      return response;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.error('Query processing failed', {
        queryId,
        userId: request.userId,
        error: error.message,
        processingTime,
      });

      throw new Error(`Query processing failed: ${error.message}`);
    }
  }

  /**
   * Search for relevant documents using vector similarity
   */
  private async searchRelevantDocuments(
    query: string,
    userId: string,
    documentIds?: string[]
  ): Promise<any[]> {
    try {
      logger.debug('Searching relevant documents', {
        userId,
        queryLength: query.length,
        documentIds: documentIds?.length,
      });

      // Step 1: Generate embedding for the query
      const queryEmbedding = await this.aiService.createEmbedding({
        text: query,
        model: 'vietnamese-embedder',
      });

      if (!queryEmbedding.embedding || queryEmbedding.embedding.length === 0) {
        logger.warn('Failed to generate query embedding');
        return [];
      }

      // Step 2: Search for similar document chunks
      const similarChunks = await this.embeddingService.searchSimilarChunks(
        queryEmbedding.embedding,
        {
          limit: 10,
          threshold: 0.6, // Minimum similarity threshold
          documentIds,
          userId,
        }
      );

      logger.debug('Document search completed', {
        userId,
        resultCount: similarChunks.length,
        avgSimilarity: similarChunks.length > 0 
          ? (similarChunks.reduce((sum, chunk) => sum + chunk.similarity, 0) / similarChunks.length).toFixed(3)
          : 0,
      });

      return similarChunks;

    } catch (error) {
      logger.error('Document search failed', {
        userId,
        error: error.message,
      });

      // Return empty array on error - query can still proceed without document context
      return [];
    }
  }

  /**
   * Calculate overall confidence based on AI response and document sources
   */
  private calculateOverallConfidence(aiConfidence: number, documentSources: any[]): number {
    if (documentSources.length === 0) {
      return aiConfidence;
    }

    // Calculate average document similarity
    const avgDocumentSimilarity = documentSources.reduce(
      (sum, source) => sum + source.similarity, 
      0
    ) / documentSources.length;

    // Weighted combination: 70% AI confidence, 30% document relevance
    const combinedConfidence = (aiConfidence * 0.7) + (avgDocumentSimilarity * 0.3);

    return Math.min(combinedConfidence, 1.0);
  }

  /**
   * Search documents by text query (for document_search query type)
   */
  async searchDocuments(
    query: string,
    userId: string,
    options?: {
      category?: string;
      limit?: number;
      offset?: number;
    }
  ): Promise<any[]> {
    try {
      logger.debug('Searching documents by text', {
        userId,
        query: query.substring(0, 50),
        options,
      });

      // Use document service for text-based search
      const documents = await this.documentService.searchDocuments(userId, query, options);

      // Also get relevant chunks using vector search
      const relevantChunks = await this.searchRelevantDocuments(query, userId);

      // Combine and deduplicate results
      const combinedResults = this.combineSearchResults(documents, relevantChunks);

      logger.debug('Document search completed', {
        userId,
        textResults: documents.length,
        vectorResults: relevantChunks.length,
        combinedResults: combinedResults.length,
      });

      return combinedResults;

    } catch (error) {
      logger.error('Document search failed', {
        userId,
        error: error.message,
      });

      throw new Error(`Document search failed: ${error.message}`);
    }
  }

  /**
   * Combine text search and vector search results
   */
  private combineSearchResults(textResults: any[], vectorResults: any[]): any[] {
    const combined = new Map();

    // Add text search results
    textResults.forEach(doc => {
      combined.set(doc.id, {
        ...doc,
        searchType: 'text',
        relevanceScore: 0.8, // Default relevance for text matches
      });
    });

    // Add vector search results (chunks)
    vectorResults.forEach(chunk => {
      const key = `${chunk.documentId}_chunk_${chunk.chunkIndex}`;
      if (!combined.has(key)) {
        combined.set(key, {
          id: chunk.documentId,
          title: chunk.document.title,
          category: chunk.document.category,
          chunkText: chunk.text,
          chunkIndex: chunk.chunkIndex,
          searchType: 'vector',
          relevanceScore: chunk.similarity,
          similarity: chunk.similarity,
        });
      }
    });

    // Convert to array and sort by relevance
    return Array.from(combined.values())
      .sort((a, b) => b.relevanceScore - a.relevanceScore);
  }
}
