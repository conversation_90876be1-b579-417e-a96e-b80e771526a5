import fs from 'fs/promises';
import path from 'path';
import pdf from 'pdf-parse';
import { logger } from '../utils/logger';
import axios from 'axios';

export interface DocumentProcessingResult {
  content: any;
  text: string;
  chunks: any[];
  embeddings: any[];
  language: string;
  confidence: number;
  pageCount?: number;
}

export interface ProcessDocumentRequest {
  filePath: string;
  originalName: string;
  mimeType: string;
  size: number;
}

export class DocumentProcessor {
  private aiServiceUrl: string;
  private timeout: number;

  constructor() {
    this.aiServiceUrl = process.env.AI_SERVICE_URL || 'http://localhost:8000';
    this.timeout = parseInt(process.env.AI_SERVICE_TIMEOUT || '30000', 10);
    logger.info('DocumentProcessor initialized', {
      aiServiceUrl: this.aiServiceUrl,
    });
  }

  /**
   * Process document and extract text content
   */
  async processDocument(request: ProcessDocumentRequest): Promise<DocumentProcessingResult> {
    const startTime = Date.now();
    
    try {
      logger.info('Starting document processing', {
        fileName: request.originalName,
        mimeType: request.mimeType,
        size: request.size,
      });

      let extractedText = '';
      let pageCount = 1;
      let confidence = 0.9;

      // Extract text based on file type
      if (request.mimeType === 'application/pdf') {
        const result = await this.extractPdfText(request.filePath);
        extractedText = result.text;
        pageCount = result.pageCount;
        confidence = result.confidence;
      } else if (request.mimeType.includes('text/')) {
        extractedText = await this.extractTextFile(request.filePath);
      } else if (request.mimeType.includes('word') || request.mimeType.includes('document')) {
        // For now, we'll handle DOC/DOCX files as text
        // In production, you'd use a library like mammoth for proper DOCX parsing
        extractedText = await this.extractTextFile(request.filePath);
        confidence = 0.7; // Lower confidence for unsupported formats
      } else {
        throw new Error(`Unsupported file type: ${request.mimeType}`);
      }

      // Clean and validate extracted text
      extractedText = this.cleanExtractedText(extractedText);
      
      if (!extractedText || extractedText.trim().length < 10) {
        throw new Error('No meaningful text content extracted from document');
      }

      // Process text with Vietnamese chunker
      const chunks = await this.chunkText(extractedText);

      // Generate embeddings for chunks
      const embeddings = await this.generateEmbeddings(chunks);

      const processingTime = Date.now() - startTime;
      
      logger.info('Document processing completed', {
        fileName: request.originalName,
        textLength: extractedText.length,
        chunkCount: chunks.length,
        pageCount,
        confidence,
        processingTime: `${processingTime}ms`,
      });

      return {
        content: {
          title: request.originalName,
          type: request.mimeType,
          size: request.size,
          processingTime,
        },
        text: extractedText,
        chunks,
        embeddings,
        language: this.detectLanguage(extractedText),
        confidence,
        pageCount,
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.error('Document processing failed', {
        error: error.message,
        fileName: request.originalName,
        processingTime: `${processingTime}ms`,
      });

      throw new Error(`Document processing failed: ${error.message}`);
    }
  }

  /**
   * Extract text from PDF file
   */
  private async extractPdfText(filePath: string): Promise<{ text: string; pageCount: number; confidence: number }> {
    try {
      const dataBuffer = await fs.readFile(filePath);
      const data = await pdf(dataBuffer);
      
      return {
        text: data.text,
        pageCount: data.numpages,
        confidence: 0.9, // High confidence for PDF extraction
      };
    } catch (error) {
      logger.error('PDF text extraction failed', { error: error.message, filePath });
      throw new Error(`Failed to extract text from PDF: ${error.message}`);
    }
  }

  /**
   * Extract text from plain text file
   */
  private async extractTextFile(filePath: string): Promise<string> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      return content;
    } catch (error) {
      logger.error('Text file reading failed', { error: error.message, filePath });
      throw new Error(`Failed to read text file: ${error.message}`);
    }
  }

  /**
   * Clean extracted text
   */
  private cleanExtractedText(text: string): string {
    return text
      .replace(/\r\n/g, '\n') // Normalize line endings
      .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
      .replace(/\s{2,}/g, ' ') // Remove excessive spaces
      .trim();
  }

  /**
   * Chunk text using Vietnamese chunker service
   */
  private async chunkText(text: string): Promise<any[]> {
    try {
      logger.debug('Chunking text with AI service', {
        textLength: text.length,
      });

      const response = await axios.post(
        `${this.aiServiceUrl}/api/documents/process`,
        {
          text,
          chunk_size: 1000,
          overlap: 100,
          preserve_structure: true,
        },
        {
          timeout: this.timeout,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const chunks = response.data.chunks || [];

      logger.debug('Text chunking completed', {
        chunkCount: chunks.length,
      });

      return chunks.map((chunk: any, index: number) => ({
        index,
        text: chunk.text,
        startChar: chunk.start_char || 0,
        endChar: chunk.end_char || chunk.text.length,
        metadata: {
          wordCount: chunk.word_count || chunk.text.split(/\s+/).length,
          charCount: chunk.char_count || chunk.text.length,
          chunkIndex: chunk.chunk_index || index,
          sectionTitle: chunk.section_title,
          legalStructure: chunk.legal_structure,
          confidence: chunk.confidence || 1.0,
        },
      }));

    } catch (error) {
      logger.warn('AI service chunking failed, falling back to simple chunking', {
        error: error.message,
      });

      // Fallback to simple chunking
      return this.simpleChunkText(text);
    }
  }

  /**
   * Simple fallback chunking method
   */
  private simpleChunkText(text: string): any[] {
    const chunkSize = 1000;
    const overlap = 200;
    const chunks = [];

    for (let i = 0; i < text.length; i += chunkSize - overlap) {
      const chunk = text.substring(i, i + chunkSize);
      if (chunk.trim().length > 0) {
        chunks.push({
          index: chunks.length,
          text: chunk.trim(),
          startChar: i,
          endChar: i + chunk.length,
          metadata: {
            wordCount: chunk.split(/\s+/).length,
            charCount: chunk.length,
          },
        });
      }
    }

    return chunks;
  }

  /**
   * Generate embeddings for chunks using AI service
   */
  private async generateEmbeddings(chunks: any[]): Promise<any[]> {
    const embeddings = [];

    try {
      logger.debug('Generating embeddings for chunks', {
        chunkCount: chunks.length,
      });

      // Process chunks in batches to avoid overwhelming the service
      const batchSize = 5;
      for (let i = 0; i < chunks.length; i += batchSize) {
        const batch = chunks.slice(i, i + batchSize);
        const batchPromises = batch.map(async (chunk, batchIndex) => {
          try {
            const response = await axios.post(
              `${this.aiServiceUrl}/api/v1/embeddings/create`,
              {
                text: chunk.text,
                model: 'vietnamese-embedder',
              },
              {
                timeout: this.timeout,
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            );

            return {
              index: i + batchIndex,
              chunkIndex: chunk.index,
              vector: response.data.embedding || [],
              model: response.data.model || 'vietnamese-embedder',
              tokensUsed: response.data.tokens_used || 0,
              metadata: {
                textLength: chunk.text.length,
                wordCount: chunk.metadata.wordCount,
                confidence: chunk.metadata.confidence || 1.0,
              },
            };
          } catch (error) {
            logger.warn('Failed to generate embedding for chunk', {
              chunkIndex: chunk.index,
              error: error.message,
            });

            // Return a placeholder embedding
            return {
              index: i + batchIndex,
              chunkIndex: chunk.index,
              vector: new Array(768).fill(0),
              model: 'fallback',
              tokensUsed: 0,
              metadata: {
                textLength: chunk.text.length,
                wordCount: chunk.metadata.wordCount,
                error: error.message,
              },
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        embeddings.push(...batchResults);

        // Small delay between batches to be respectful to the service
        if (i + batchSize < chunks.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      logger.debug('Embedding generation completed', {
        embeddingCount: embeddings.length,
        successfulEmbeddings: embeddings.filter(e => e.model !== 'fallback').length,
      });

      return embeddings;

    } catch (error) {
      logger.error('Embedding generation failed', {
        error: error.message,
        chunkCount: chunks.length,
      });

      // Return placeholder embeddings
      return chunks.map((chunk, index) => ({
        index,
        chunkIndex: chunk.index,
        vector: new Array(768).fill(0),
        model: 'fallback',
        tokensUsed: 0,
        metadata: {
          textLength: chunk.text.length,
          wordCount: chunk.metadata.wordCount,
          error: 'Embedding service unavailable',
        },
      }));
    }
  }

  /**
   * Detect document language
   */
  private detectLanguage(text: string): string {
    // Simple Vietnamese detection
    const vietnameseChars = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;
    
    if (vietnameseChars.test(text)) {
      return 'vi';
    }
    
    return 'en'; // Default to English
  }
}
