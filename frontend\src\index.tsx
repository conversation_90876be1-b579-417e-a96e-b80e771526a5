import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';

import { CssBaseline } from '@mui/material';
import { HelmetProvider } from 'react-helmet-async';
import { I18nextProvider } from 'react-i18next';

import App from './App';
import { store } from './store/store';
import i18n from './i18n/i18n';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { PWAInstallPrompt } from './components/common/PWAInstallPrompt';
import { AuthProvider } from './components/providers/AuthProvider';
import { ThemeProvider as CustomThemeProvider } from './components/providers/ThemeProvider';
import reportWebVitals from './utils/reportWebVitals';
import * as serviceWorkerRegistration from './serviceWorkerRegistration';

// React Query client configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// SEO and Analytics setup
const setupAnalytics = () => {
  // Google Analytics 4
  if (process.env.REACT_APP_GA_MEASUREMENT_ID) {
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${process.env.REACT_APP_GA_MEASUREMENT_ID}`;
    document.head.appendChild(script);

    window.dataLayer = window.dataLayer || [];
    const gtag = (...args: any[]) => {
      window.dataLayer.push(args);
    };
    window.gtag = gtag;
    gtag('js', new Date());
    gtag('config', process.env.REACT_APP_GA_MEASUREMENT_ID, {
      page_title: 'LegalMind Pro - AI Assistant Pháp Luật Thông Minh',
      page_location: window.location.href,
      custom_map: {
        custom_parameter_1: 'legal_ai_usage',
        custom_parameter_2: 'vietnamese_legal_queries',
      },
    });

    // Enhanced ecommerce tracking for legal services
    gtag('config', process.env.REACT_APP_GA_MEASUREMENT_ID, {
      custom_map: {
        dimension1: 'user_type',
        dimension2: 'legal_category',
        dimension3: 'query_complexity',
      },
    });
  }

  // Microsoft Clarity for user behavior analytics
  if (process.env.REACT_APP_CLARITY_PROJECT_ID) {
    (function(c: any, l: any, a: any, r: any, i: any) {
      c[a] = c[a] || function() { (c[a].q = c[a].q || []).push(arguments); };
      const t = l.createElement(r);
      t.async = 1;
      t.src = "https://www.clarity.ms/tag/" + i;
      const y = l.getElementsByTagName(r)[0];
      y.parentNode.insertBefore(t, y);
    })(window, document, "clarity", "script", process.env.REACT_APP_CLARITY_PROJECT_ID);
  }
};

// Performance monitoring
const setupPerformanceMonitoring = () => {
  // Core Web Vitals tracking
  reportWebVitals((metric) => {
    // Send to analytics
    if (window.gtag) {
      window.gtag('event', metric.name, {
        event_category: 'Web Vitals',
        event_label: metric.id,
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        non_interaction: true,
      });
    }

    // Send to custom monitoring endpoint
    if (process.env.REACT_APP_MONITORING_ENDPOINT) {
      fetch(process.env.REACT_APP_MONITORING_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric: metric.name,
          value: metric.value,
          id: metric.id,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        }),
      }).catch(console.error);
    }
  });
};

// Initialize app
const initializeApp = () => {
  setupAnalytics();
  setupPerformanceMonitoring();

  // Register service worker for PWA functionality
  if (process.env.NODE_ENV === 'production') {
    serviceWorkerRegistration.register({
      onSuccess: () => {
        console.log('LegalMind Pro is now available offline!');
      },
      onUpdate: (registration) => {
        console.log('New version available! Please refresh to update.');
        // Show update notification to user
        if (window.confirm('New version available! Refresh to update?')) {
          window.location.reload();
        }
      },
    });
  }
};

// App component with all providers
const AppWithProviders: React.FC = () => {
  React.useEffect(() => {
    initializeApp();
  }, []);

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <Provider store={store}>
          <QueryClientProvider client={queryClient}>
            <AuthProvider>
              <CustomThemeProvider>
                <I18nextProvider i18n={i18n}>
                  <CssBaseline />
                  <BrowserRouter>
                    <App />
                    <PWAInstallPrompt />
                  </BrowserRouter>
                </I18nextProvider>
              </CustomThemeProvider>
              {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}
            </AuthProvider>
          </QueryClientProvider>
        </Provider>
      </HelmetProvider>
    </ErrorBoundary>
  );
};

// Render app
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(<AppWithProviders />);

// Type declarations for global objects
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
    clarity: (action: string, key: string, value: any) => void;
  }
}

export default AppWithProviders;
